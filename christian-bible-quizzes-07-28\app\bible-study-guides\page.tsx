import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { bibleBooks } from '@/data/bible-books';

export const metadata: Metadata = {
  title: 'Bible Study Guides | Christian Bible Quizzes',
  description: 'Comprehensive Bible study guides for all 66 books. Chapter-by-chapter studies, character profiles, and thematic guides to deepen your understanding of Scripture.',
  keywords: ['bible study guides', 'scripture study', 'bible commentary', 'chapter studies', 'bible reading plans', 'biblical theology'],
  openGraph: {
    title: 'Bible Study Guides - Deep Scripture Studies',
    description: 'Comprehensive Bible study guides covering all 66 books with chapter-by-chapter analysis and practical applications.',
    images: ['/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png'],
  },
};

export default function BibleStudyGuidesPage() {
  const oldTestamentBooks = bibleBooks.filter(book => book.testament === 'old');
  const newTestamentBooks = bibleBooks.filter(book => book.testament === 'new');

  const studyCategories = [
    {
      title: 'Book Studies',
      description: 'In-depth studies of complete Bible books with historical context and theological insights',
      icon: '📚',
      href: '/book-studies',
      color: 'blue'
    },
    {
      title: 'Character Studies',
      description: 'Deep dives into biblical characters, their lives, and lessons for today',
      icon: '👥', 
      href: '/character-studies',
      color: 'green'
    },
    {
      title: 'Thematic Studies',
      description: 'Explore major biblical themes like salvation, love, faith, and prophecy',
      icon: '🎯',
      href: '/thematic-studies', 
      color: 'purple'
    },
    {
      title: 'Reading Plans',
      description: 'Structured plans to read through the Bible systematically',
      icon: '📅',
      href: '/reading-plans',
      color: 'orange'
    },
    {
      title: 'Small Group Guides',
      description: 'Discussion questions and activities for Bible study groups',
      icon: '💬',
      href: '/small-group-guides',
      color: 'red'
    },
    {
      title: 'Youth Studies',
      description: 'Age-appropriate Bible studies designed specifically for teens and young adults',
      icon: '🌟',
      href: '/youth-studies',
      color: 'indigo'
    }
  ];

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[500px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
              alt="Bible Study Guides"
              fill
              className="object-cover opacity-30"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/80 to-indigo-700/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Bible Study
              <span className="block text-yellow-300">Guides</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Deepen your understanding of Scripture with comprehensive study guides for all 66 Bible books. 
              Perfect for personal study, small groups, and Bible classes.
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">66</div>
                <div className="text-sm text-gray-200">Book Guides</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">1,189</div>
                <div className="text-sm text-gray-200">Chapters</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">200+</div>
                <div className="text-sm text-gray-200">Characters</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">50+</div>
                <div className="text-sm text-gray-200">Themes</div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-study-guide"
                className="bg-yellow-500 hover:bg-yellow-600 text-indigo-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start with Genesis
              </Link>
              <Link
                href="#categories"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-indigo-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Browse Categories
              </Link>
            </div>
          </div>
        </section>

        {/* Study Categories */}
        <section id="categories" className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Study Guide Categories
              </h2>
              <p className="text-xl text-gray-600">
                Choose your preferred style of Bible study for deeper spiritual growth
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {studyCategories.map((category, index) => (
                <div key={index} className={`bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group border-t-4 border-${category.color}-500`}>
                  <div className="p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="text-3xl">{category.icon}</div>
                      <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                    </div>
                    <p className="text-gray-600 mb-6">
                      {category.description}
                    </p>
                    <Link
                      href={category.href}
                      className={`inline-flex items-center bg-${category.color}-600 hover:bg-${category.color}-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200`}
                    >
                      Explore Studies
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Old Testament Studies */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Old Testament Studies
              </h2>
              <p className="text-xl text-gray-600">
                Start with these foundational books of the Hebrew Scriptures
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {oldTestamentBooks.slice(0, 6).map((book) => (
                <div key={book.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={book.imageUrl || '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png'}
                      alt={book.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{book.name}</h3>
                      <p className="text-sm opacity-90">{book.chapters} Chapters • Old Testament</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {book.description}
                    </p>
                    <div className="mb-4">
                      <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Key Themes</div>
                      <div className="flex flex-wrap gap-1">
                        {book.keyThemes.slice(0, 3).map((theme, index) => (
                          <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            {theme}
                          </span>
                        ))}
                      </div>
                    </div>
                    <Link
                      href={`/${book.slug}-study-guide`}
                      className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Study Guide
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/old-testament-study-guides"
                className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                All Old Testament Studies
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Featured New Testament Studies */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured New Testament Studies
              </h2>
              <p className="text-xl text-gray-600">
                Explore the Gospel message and early church teachings
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {newTestamentBooks.slice(0, 6).map((book) => (
                <div key={book.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={book.imageUrl || '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png'}
                      alt={book.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{book.name}</h3>
                      <p className="text-sm opacity-90">{book.chapters} Chapters • New Testament</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {book.description}
                    </p>
                    <div className="mb-4">
                      <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Key Themes</div>
                      <div className="flex flex-wrap gap-1">
                        {book.keyThemes.slice(0, 3).map((theme, index) => (
                          <span key={index} className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                            {theme}
                          </span>
                        ))}
                      </div>
                    </div>
                    <Link
                      href={`/${book.slug}-study-guide`}
                      className="inline-flex items-center bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Study Guide
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/new-testament-study-guides"
                className="inline-flex items-center bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                All New Testament Studies
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-indigo-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Dive Deeper into Scripture?
            </h2>
            <p className="text-xl text-indigo-100 mb-8 max-w-3xl mx-auto">
              Our comprehensive study guides provide the historical context, theological insights, 
              and practical applications you need for meaningful Bible study.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-study-guide"
                className="bg-white hover:bg-gray-100 text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start Your Journey
              </Link>
              <Link
                href="/bible-quizzes"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-indigo-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Take a Quiz First
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Resources</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/book-studies" className="block hover:text-white">Book Studies</Link>
                <Link href="/character-studies" className="block hover:text-white">Character Studies</Link>
                <Link href="/thematic-studies" className="block hover:text-white">Thematic Studies</Link>
                <Link href="/bible-study-guides" className="block hover:text-white">All Study Guides</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Popular Studies</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/genesis-study-guide" className="block hover:text-white">Genesis Study</Link>
                <Link href="/john-study-guide" className="block hover:text-white">Gospel of John</Link>
                <Link href="/romans-study-guide" className="block hover:text-white">Romans Study</Link>
                <Link href="/psalms-study-guide" className="block hover:text-white">Psalms Study</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Plans</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/reading-plans" className="block hover:text-white">Reading Plans</Link>
                <Link href="/small-group-guides" className="block hover:text-white">Small Group Guides</Link>
                <Link href="/youth-studies" className="block hover:text-white">Youth Studies</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Site Info</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/about" className="block hover:text-white">About Us</Link>
                <Link href="/contact" className="block hover:text-white">Contact</Link>
                <Link href="/privacy" className="block hover:text-white">Privacy Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 Christian Bible Quizzes. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: 'Bible Study Guides',
            description: 'Comprehensive Bible study guides for all 66 books with chapter-by-chapter analysis and practical applications.',
            url: 'https://yourdomain.com/bible-study-guides/',
            mainEntity: {
              '@type': 'ItemList',
              numberOfItems: bibleBooks.length,
              itemListElement: bibleBooks.slice(0, 10).map((book, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: `${book.name} Study Guide`,
                url: `https://yourdomain.com/${book.slug}-study-guide/`
              }))
            }
          })
        }}
      />
    </>
  );
}