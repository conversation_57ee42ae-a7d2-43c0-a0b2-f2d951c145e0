import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { bibleBooks, getBooksByTestament } from '@/data/bible-books';

export default function Home() {
  const oldTestamentBooks = getBooksByTestament('old');
  const newTestamentBooks = getBooksByTestament('new');

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section - Using Discovered Bible Image */}
        <section className="relative min-h-[600px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png"
              alt="Bible verse background"
              fill
              className="object-cover opacity-20"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Test Your
              <span className="block text-yellow-300">Bible Knowledge</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. 
              Perfect for Bible study groups, Sunday school, and personal spiritual growth.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/bible-quizzes"
                className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start Quiz Journey
              </Link>
              <Link
                href="/genesis-quiz"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Try Genesis Quiz
              </Link>
            </div>
          </div>
        </section>

        {/* Quiz Statistics */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-blue-600">1,189</div>
                <div className="text-gray-600 font-medium">Chapter Quizzes</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-purple-600">66</div>
                <div className="text-gray-600 font-medium">Bible Books</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-green-600">200+</div>
                <div className="text-gray-600 font-medium">Characters</div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl md:text-4xl font-bold text-red-600">1,000+</div>
                <div className="text-gray-600 font-medium">Questions</div>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Quizzes - Using Discovered Images */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Bible Quizzes
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Start your journey with these popular Bible quizzes covering both Old and New Testament
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Genesis Quiz Card */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48">
                  <Image
                    src="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png"
                    alt="Genesis Quiz"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">Genesis Quiz</h3>
                    <p className="text-sm opacity-90">50 Chapters • 25 Questions</p>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    Test your knowledge of the book of beginnings - creation, Abraham, Isaac, Jacob, and Joseph.
                  </p>
                  <Link
                    href="/genesis-quiz"
                    className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                  >
                    Take Quiz
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Matthew Quiz Card */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48">
                  <Image
                    src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                    alt="Matthew Quiz"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">Matthew Quiz</h3>
                    <p className="text-sm opacity-90">28 Chapters • 25 Questions</p>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    Explore the Gospel of Matthew and Jesus as the promised Messiah and King.
                  </p>
                  <Link
                    href="/matthew-quiz"
                    className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                  >
                    Take Quiz
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* Psalms Quiz Card */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48">
                  <Image
                    src="/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png"
                    alt="Psalms Quiz"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">Psalms Quiz</h3>
                    <p className="text-sm opacity-90">150 Chapters • 25 Questions</p>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    Dive into the book of worship, prayers, and songs of praise to God.
                  </p>
                  <Link
                    href="/psalms-quiz"
                    className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                  >
                    Take Quiz
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Bible Books Overview */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                All Bible Books
              </h2>
              <p className="text-xl text-gray-600">
                Choose from any of the 66 books of the Bible for comprehensive quizzes
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Old Testament */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
                      alt="Old Testament"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Old Testament</h3>
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    39 Books
                  </span>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {oldTestamentBooks.slice(0, 12).map((book) => (
                    <Link
                      key={book.id}
                      href={`/${book.slug}-quiz`}
                      className="bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-gray-700 group-hover:text-blue-600">
                        {book.name}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {book.chapters} chapters
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center">
                  <Link
                    href="/old-testament-quizzes"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View all Old Testament books
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* New Testament */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
                      alt="New Testament"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">New Testament</h3>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                    27 Books
                  </span>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {newTestamentBooks.slice(0, 12).map((book) => (
                    <Link
                      key={book.id}
                      href={`/${book.slug}-quiz`}
                      className="bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg p-3 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-gray-700 group-hover:text-purple-600">
                        {book.name}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {book.chapters} chapters
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center">
                  <Link
                    href="/new-testament-quizzes"
                    className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                  >
                    View all New Testament books
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center justify-center opacity-10">
                <Image
                  src="/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png"
                  alt="Bible study background"
                  width={200}
                  height={200}
                  className="object-cover rounded-full"
                />
              </div>
              <h2 className="relative text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to Begin Your Bible Quiz Journey?
              </h2>
            </div>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Join thousands of believers growing in their faith through interactive Bible study. 
              Start with any book or topic that interests you most.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/bible-quizzes"
                className="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Browse All Quizzes
              </Link>
              <Link
                href="/bible-characters"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Character Studies
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Bible Quizzes</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/genesis-quiz" className="block hover:text-white">Genesis Quiz</Link>
                <Link href="/matthew-quiz" className="block hover:text-white">Matthew Quiz</Link>
                <Link href="/psalms-quiz" className="block hover:text-white">Psalms Quiz</Link>
                <Link href="/bible-quizzes" className="block hover:text-white">All Quizzes</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Resources</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/bible-study-guides" className="block hover:text-white">Study Guides</Link>
                <Link href="/bible-characters" className="block hover:text-white">Character Studies</Link>
                <Link href="/bible-reading-plans" className="block hover:text-white">Reading Plans</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Popular Topics</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/miracles-quiz" className="block hover:text-white">Miracles Quiz</Link>
                <Link href="/parables-quiz" className="block hover:text-white">Parables Quiz</Link>
                <Link href="/ten-commandments-quiz" className="block hover:text-white">Ten Commandments</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Site Info</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/about" className="block hover:text-white">About Us</Link>
                <Link href="/contact" className="block hover:text-white">Contact</Link>
                <Link href="/privacy" className="block hover:text-white">Privacy Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 Christian Bible Quizzes. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </>
  );
}