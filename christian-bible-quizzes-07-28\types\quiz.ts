export type QuestionType = 'multiple-choice' | 'true-false' | 'fill-blank' | 'matching' | 'ordering';

export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'expert';

export type QuizCategory = 'chapter' | 'book' | 'character' | 'theme' | 'event' | 'difficulty';

export interface QuizQuestion {
  id: string;
  question: string;
  type: QuestionType;
  options?: string[]; // For multiple-choice
  correctAnswer: string | number | boolean;
  explanation: string;
  bibleReference: string;
  difficulty: DifficultyLevel;
  tags: string[];
  imageUrl?: string; // For image-based questions using discovered assets
}

export interface Quiz {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: QuizCategory;
  difficulty: DifficultyLevel;
  questions: QuizQuestion[];
  estimatedTime: number; // in minutes
  tags: string[];
  isBookQuiz: boolean;
  bookName?: string;
  chapterNumber?: number;
  characterName?: string;
  theme?: string;
  imageUrl?: string; // Hero image from discovered assets
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  publishedAt: string;
  updatedAt: string;
}

export interface BibleBook {
  id: string;
  name: string;
  slug: string;
  testament: 'old' | 'new';
  chapters: number;
  order: number;
  abbreviation: string;
  description: string;
  keyThemes: string[];
  mainCharacters: string[];
  imageUrl?: string; // From discovered assets
}

export interface BibleCharacter {
  id: string;
  name: string;
  slug: string;
  testament: 'old' | 'new' | 'both';
  description: string;
  keyVerses: string[];
  relatedBooks: string[];
  significance: string;
  timesPeriod: string;
  imageUrl?: string; // From discovered assets
  aliases: string[];
}

export interface QuizResult {
  quizId: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: {
    questionId: string;
    userAnswer: string | number | boolean;
    isCorrect: boolean;
    timeSpent: number;
  }[];
  completedAt: string;
  difficulty: DifficultyLevel;
}

export interface QuizStats {
  totalQuizzes: number;
  completedQuizzes: number;
  averageScore: number;
  totalTimeSpent: number;
  favoriteBooks: string[];
  strengths: string[];
  areasForImprovement: string[];
  achievements: string[];
}

// Navigation and UI Types
export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  icon?: string;
  imageUrl?: string; // For navigation icons using discovered assets
}

export interface QuizCard {
  quiz: Quiz;
  completionRate?: number;
  averageScore?: number;
  isCompleted?: boolean;
  imageUrl?: string; // Card image from discovered assets
}

// SEO and Schema Types
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  ogImage: string;
  structuredData: any; // JSON-LD schema
}

export interface SchemaQuiz {
  '@context': string;
  '@type': string;
  name: string;
  description: string;
  about: {
    '@type': string;
    name: string;
  };
  educationalLevel: string;
  assesses: string;
  typicalAgeRange: string;
  timeRequired: string;
  interactivityType: string;
  learningResourceType: string;
  hasPart: SchemaQuestion[];
}

export interface SchemaQuestion {
  '@type': string;
  name: string;
  answerCount: number;
  acceptedAnswer: {
    '@type': string;
    text: string;
  };
}

// Hub Page Types
export interface HubPageData {
  title: string;
  description: string;
  featuredQuizzes: Quiz[];
  categories: {
    name: string;
    count: number;
    href: string;
    imageUrl?: string; // Category image from discovered assets
  }[];
  stats: {
    totalQuizzes: number;
    totalQuestions: number;
    totalUsers: number;
    averageCompletion: number;
  };
  recentQuizzes: Quiz[];
  popularQuizzes: Quiz[];
}

// Theme Configuration
export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
    muted: string;
  };
  fonts: {
    heading: string;
    body: string;
    mono: string;
  };
  images: {
    hero: string; // Main hero image from discovered assets
    navigation: string; // Navigation background
    cards: string[]; // Array of card images
    backgrounds: string[]; // Background patterns
  };
}