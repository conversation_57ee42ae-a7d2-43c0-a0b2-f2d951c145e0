import { <PERSON><PERSON>hara<PERSON> } from '@/types/quiz';

export const bible<PERSON>haracters: BibleCharacter[] = [
  // OLD TESTAMENT CHARACTERS
  {
    id: 'abraham',
    name: '<PERSON>',
    slug: 'abraham',
    testament: 'old',
    description: 'The father of faith, called by God to leave his homeland and establish a covenant that would bless all nations.',
    keyVerses: ['Genesis 12:1-3', 'Romans 4:16', 'Hebrews 11:8-12'],
    relatedBooks: ['Genesis', 'Romans', 'Hebrews'],
    significance: 'Father of the Jewish nation and example of faith for all believers',
    timesPeriod: 'c. 2100-1975 BC',
    imageUrl: '/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png',
    aliases: ['<PERSON><PERSON><PERSON>']
  },
  {
    id: 'moses',
    name: '<PERSON>',
    slug: 'moses',
    testament: 'old',
    description: 'The great lawgiver and prophet who led Israel out of Egypt and received the Ten Commandments on Mount Sinai.',
    keyVerses: ['Exodus 3:1-15', 'Deuteronomy 34:10-12', 'Hebrews 11:24-29'],
    relatedBooks: ['Exodus', 'Levi<PERSON>', 'Numbers', 'Deuteronomy'],
    significance: 'Deliverer of Israel from Egypt and mediator of the Old Covenant',
    timesPeriod: 'c. 1393-1273 BC',
    imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png',
    aliases: []
  },
  {
    id: 'david',
    name: 'David',
    slug: 'david',
    testament: 'old',
    description: 'The shepherd boy who became king of Israel, known as a man after God\'s own heart and author of many Psalms.',
    keyVerses: ['1 Samuel 16:7', '2 Samuel 7:12-16', 'Acts 13:22'],
    relatedBooks: ['1 Samuel', '2 Samuel', '1 Kings', 'Psalms'],
    significance: 'Greatest king of Israel and ancestor of Jesus Christ',
    timesPeriod: 'c. 1040-970 BC',
    imageUrl: '/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png',
    aliases: []
  },
  {
    id: 'solomon',
    name: 'Solomon',
    slug: 'solomon',
    testament: 'old',
    description: 'The wisest king of Israel who built the first temple and wrote much of Proverbs, Ecclesiastes, and Song of Songs.',
    keyVerses: ['1 Kings 3:5-14', '1 Kings 6:1-38', '2 Chronicles 1:7-12'],
    relatedBooks: ['1 Kings', '2 Chronicles', 'Proverbs', 'Ecclesiastes', 'Song of Songs'],
    significance: 'Wisest man who ever lived and builder of the first temple',
    timesPeriod: 'c. 990-931 BC',
    imageUrl: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png',
    aliases: []
  },
  {
    id: 'noah',
    name: 'Noah',
    slug: 'noah',
    testament: 'old',
    description: 'The righteous man who built the ark and saved humanity and animals from the worldwide flood.',
    keyVerses: ['Genesis 6:9', 'Genesis 7:1', 'Hebrews 11:7'],
    relatedBooks: ['Genesis'],
    significance: 'Preserver of humanity through God\'s judgment of the flood',
    timesPeriod: 'c. 2900-2000 BC',
    imageUrl: '/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png',
    aliases: []
  },
  {
    id: 'joseph',
    name: 'Joseph',
    slug: 'joseph',
    testament: 'old',
    description: 'The son of Jacob who was sold into slavery but became second ruler of Egypt, saving his family from famine.',
    keyVerses: ['Genesis 37:3-4', 'Genesis 50:20', 'Acts 7:9-16'],
    relatedBooks: ['Genesis', 'Acts'],
    significance: 'Preserver of the chosen family through divine providence',
    timesPeriod: 'c. 1915-1805 BC',
    imageUrl: '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png',
    aliases: []
  },

  // NEW TESTAMENT CHARACTERS
  {
    id: 'jesus',
    name: 'Jesus Christ',
    slug: 'jesus',
    testament: 'both',
    description: 'The Son of God, Messiah, and Savior of the world who lived a perfect life, died for our sins, and rose again.',
    keyVerses: ['John 3:16', 'Romans 5:8', '1 Corinthians 15:3-4'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', 'Romans', 'Hebrews', 'Revelation'],
    significance: 'The central figure of Christianity, God incarnate, Savior of humanity',
    timesPeriod: 'c. 4 BC - 30 AD',
    imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png',
    aliases: ['Jesus of Nazareth', 'Christ', 'Messiah', 'Son of God', 'Son of Man']
  },
  {
    id: 'paul',
    name: 'Paul the Apostle',
    slug: 'paul',
    testament: 'new',
    description: 'The former persecutor of Christians who became the greatest missionary and wrote much of the New Testament.',
    keyVerses: ['Acts 9:1-19', '1 Corinthians 15:9-10', 'Philippians 3:7-8'],
    relatedBooks: ['Acts', 'Romans', '1 Corinthians', '2 Corinthians', 'Galatians', 'Ephesians', 'Philippians', 'Colossians', '1 Thessalonians', '2 Thessalonians', '1 Timothy', '2 Timothy', 'Titus', 'Philemon'],
    significance: 'Apostle to the Gentiles and primary theologian of the early church',
    timesPeriod: 'c. 5-67 AD',
    imageUrl: '/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png',
    aliases: ['Saul of Tarsus', 'Saul']
  },
  {
    id: 'peter',
    name: 'Peter',
    slug: 'peter',
    testament: 'new',
    description: 'The fisherman who became the leader of the twelve apostles and the early church after Jesus\' resurrection.',
    keyVerses: ['Matthew 16:16-18', 'Acts 2:14-41', '1 Peter 5:1-4'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts', '1 Peter', '2 Peter'],
    significance: 'Leader of the twelve apostles and foundation of the early church',
    timesPeriod: 'c. 1-67 AD',
    imageUrl: '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png',
    aliases: ['Simon Peter', 'Simon', 'Cephas']
  },
  {
    id: 'john',
    name: 'John the Apostle',
    slug: 'john',
    testament: 'new',
    description: 'The beloved disciple who wrote the Gospel of John, three epistles, and the book of Revelation.',
    keyVerses: ['John 13:23', 'John 19:26-27', '1 John 4:7-8'],
    relatedBooks: ['John', '1 John', '2 John', '3 John', 'Revelation'],
    significance: 'The beloved disciple and author of five New Testament books',
    timesPeriod: 'c. 6-100 AD',
    imageUrl: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png',
    aliases: ['John the Beloved', 'Son of Thunder']
  },
  {
    id: 'mary',
    name: 'Mary',
    slug: 'mary',
    testament: 'new',
    description: 'The virgin chosen by God to be the mother of Jesus Christ, showing faith and obedience to God\'s will.',
    keyVerses: ['Luke 1:26-38', 'Luke 2:19', 'John 19:25-27'],
    relatedBooks: ['Matthew', 'Mark', 'Luke', 'John', 'Acts'],
    significance: 'Mother of Jesus Christ and example of faithful submission to God',
    timesPeriod: 'c. 18 BC - 41 AD',
    imageUrl: '/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png',
    aliases: ['Mary of Nazareth', 'Virgin Mary', 'Mother of Jesus']
  },

  // WOMEN OF THE BIBLE
  {
    id: 'ruth',
    name: 'Ruth',
    slug: 'ruth',
    testament: 'old',
    description: 'The Moabite woman who showed loyal love to her mother-in-law Naomi and became an ancestor of Jesus.',
    keyVerses: ['Ruth 1:16-17', 'Ruth 4:13-17'],
    relatedBooks: ['Ruth', 'Matthew'],
    significance: 'Example of loyal love and faithfulness, ancestor of Jesus',
    timesPeriod: 'c. 1100 BC',
    imageUrl: '/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png',
    aliases: []
  },
  {
    id: 'esther',
    name: 'Esther',
    slug: 'esther',
    testament: 'old',
    description: 'The Jewish queen of Persia who risked her life to save her people from genocide.',
    keyVerses: ['Esther 4:14', 'Esther 7:3-6'],
    relatedBooks: ['Esther'],
    significance: 'Savior of the Jewish people during the Persian period',
    timesPeriod: 'c. 519-465 BC',
    imageUrl: '/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png',
    aliases: ['Hadassah']
  },
  {
    id: 'deborah',
    name: 'Deborah',
    slug: 'deborah',
    testament: 'old',
    description: 'The prophet and judge who led Israel to victory over the Canaanites and sang a song of triumph.',
    keyVerses: ['Judges 4:4-5', 'Judges 5:1-31'],
    relatedBooks: ['Judges'],
    significance: 'Only female judge of Israel and military leader',
    timesPeriod: 'c. 1200 BC',
    imageUrl: '/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png',
    aliases: []
  }
];

// Helper functions
export const getCharacterBySlug = (slug: string): BibleCharacter | undefined => {
  return bibleCharacters.find(character => character.slug === slug);
};

export const getCharactersByTestament = (testament: 'old' | 'new' | 'both'): BibleCharacter[] => {
  return bibleCharacters.filter(character => 
    character.testament === testament || character.testament === 'both'
  );
};

export const getAllCharacterSlugs = (): string[] => {
  return bibleCharacters.map(character => character.slug);
};

export const getWomenOfTheBible = (): BibleCharacter[] => {
  const womenNames = ['Mary', 'Ruth', 'Esther', 'Deborah'];
  return bibleCharacters.filter(character => 
    womenNames.some(name => character.name.includes(name))
  );
};

export const getMajorPatriarchs = (): BibleCharacter[] => {
  const patriarchNames = ['Abraham', 'Isaac', 'Jacob'];
  return bibleCharacters.filter(character => 
    patriarchNames.includes(character.name)
  );
};

export const getApostles = (): BibleCharacter[] => {
  const apostleNames = ['Paul', 'Peter', 'John'];
  return bibleCharacters.filter(character => 
    apostleNames.some(name => character.name.includes(name))
  );
};