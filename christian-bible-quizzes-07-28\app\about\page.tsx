import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';

export const metadata: Metadata = {
  title: 'About Us | Christian Bible Quizzes',
  description: 'Learn about our mission to provide comprehensive Bible quizzes and study resources for Christians of all ages and backgrounds.',
  keywords: ['about us', 'bible study', 'christian education', 'scripture learning', 'bible quiz mission'],
  openGraph: {
    title: 'About Christian Bible Quizzes',
    description: 'Discover our passion for helping Christians grow in their knowledge of Scripture through interactive quizzes and study guides.',
    images: ['/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png'],
  },
};

export default function AboutPage() {
  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[400px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png"
              alt="About Christian Bible Quizzes"
              fill
              className="object-cover opacity-30"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 to-green-700/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              About Our
              <span className="block text-yellow-300">Mission</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Helping believers grow in their knowledge of Scripture through interactive quizzes, 
              comprehensive study guides, and biblical resources.
            </p>
          </div>
        </section>

        {/* Mission Statement */}
        <section className="py-16 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Mission
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                We believe that knowing God's Word is essential for every believer's spiritual growth. 
                Our mission is to make Bible study engaging, accessible, and comprehensive through 
                interactive quizzes and educational resources that help Christians of all ages and 
                backgrounds deepen their understanding of Scripture.
              </p>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Our Values
              </h2>
              <p className="text-xl text-gray-600">
                The principles that guide everything we do
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <div className="relative w-16 h-16 mx-auto mb-6">
                  <Image
                    src="/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png"
                    alt="Biblical Accuracy"
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Biblical Accuracy</h3>
                <p className="text-gray-600">
                  Every quiz question and study guide is carefully researched and reviewed 
                  for theological accuracy and biblical faithfulness.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <div className="relative w-16 h-16 mx-auto mb-6">
                  <Image
                    src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                    alt="Accessibility"
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Accessibility</h3>
                <p className="text-gray-600">
                  We design our resources to be accessible to all learners, from new believers 
                  to mature Christians seeking to deepen their knowledge.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <div className="relative w-16 h-16 mx-auto mb-6">
                  <Image
                    src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
                    alt="Community"
                    fill
                    className="object-cover rounded-full"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">Community</h3>
                <p className="text-gray-600">
                  We foster a community of learners who encourage one another in their 
                  spiritual journey and biblical understanding.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* What We Offer */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What We Offer
              </h2>
              <p className="text-xl text-gray-600">
                Comprehensive resources for biblical learning
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Interactive Bible Quizzes</h3>
                    <p className="text-gray-600">
                      Over 1,000 quizzes covering all 66 books of the Bible with multiple question formats and difficulty levels.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Comprehensive Study Guides</h3>
                    <p className="text-gray-600">
                      In-depth study materials for every Bible book, including historical context and theological insights.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Character Studies</h3>
                    <p className="text-gray-600">
                      Explore the lives and lessons of over 200 biblical characters from both testaments.
                    </p>
                  </div>
                </div>
              </div>

              <div className="relative h-96">
                <Image
                  src="/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png"
                  alt="Bible Study Resources"
                  fill
                  className="object-cover rounded-xl shadow-lg"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-green-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Start Your Bible Study Journey Today
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              Whether you're a new believer or have been walking with Christ for years, 
              our resources will help you grow in your knowledge and love of God's Word.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/bible-quizzes"
                className="bg-white hover:bg-gray-100 text-green-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Take a Quiz
              </Link>
              <Link
                href="/bible-study-guides"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-green-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Browse Study Guides
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'AboutPage',
            name: 'About Christian Bible Quizzes',
            description: 'Learn about our mission to provide comprehensive Bible quizzes and study resources for Christians of all ages and backgrounds.',
            url: 'https://yourdomain.com/about/',
            mainEntity: {
              '@type': 'Organization',
              name: 'Christian Bible Quizzes',
              description: 'Helping believers grow in their knowledge of Scripture through interactive quizzes, comprehensive study guides, and biblical resources.',
              url: 'https://yourdomain.com',
              foundingDate: '2025'
            }
          })
        }}
      />
    </>
  );
}