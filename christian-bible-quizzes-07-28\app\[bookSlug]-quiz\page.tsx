import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { getBookBySlug, getChapterSlugs, getAllBookSlugs, bibleBooks } from '@/data/bible-books';

interface BookQuizPageProps {
  params: {
    bookSlug: string;
  };
}

export async function generateStaticParams() {
  const bookSlugs = getAllBookSlugs();
  
  return bookSlugs.map((bookSlug) => ({
    bookSlug,
  }));
}

export async function generateMetadata({ params }: BookQuizPageProps): Promise<Metadata> {
  const book = getBookBySlug(params.bookSlug);
  
  if (!book) {
    return {
      title: 'Book Not Found',
    };
  }

  return {
    title: `${book.name} Quiz - Test Your Bible Knowledge | Christian Bible Quizzes`,
    description: `Comprehensive ${book.name} quiz with ${book.chapters} chapter quizzes and complete book quiz. Test your knowledge of this ${book.testament === 'old' ? 'Old' : 'New'} Testament book with detailed questions and explanations.`,
    keywords: [`${book.name.toLowerCase()} quiz`, 'bible quiz', `${book.name.toLowerCase()} chapter quiz`, `${book.testament} testament quiz`],
    openGraph: {
      title: `${book.name} Quiz - Bible Knowledge Test`,
      description: `Test your knowledge of ${book.name} with comprehensive chapter-by-chapter quizzes and complete book assessment.`,
      images: [book.imageUrl || '/images/default-bible.png'],
    },
  };
}

export default function BookQuizPage({ params }: BookQuizPageProps) {
  const book = getBookBySlug(params.bookSlug);
  
  if (!book) {
    notFound();
  }

  const chapterSlugs = getChapterSlugs(params.bookSlug);
  const testament = book.testament === 'old' ? 'Old Testament' : 'New Testament';
  const testamentColor = book.testament === 'old' ? 'blue' : 'purple';

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[400px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src={book.imageUrl || '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png'}
              alt={`${book.name} background`}
              fill
              className="object-cover opacity-30"
            />
            <div className={`absolute inset-0 ${book.testament === 'old' ? 'bg-gradient-to-r from-blue-900/80 to-blue-700/60' : 'bg-gradient-to-r from-purple-900/80 to-purple-700/60'}`} />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <div className="mb-4">
              <span className={`inline-block px-4 py-2 rounded-full text-sm font-medium ${book.testament === 'old' ? 'bg-blue-500/20 text-blue-200' : 'bg-purple-500/20 text-purple-200'}`}>
                {testament} • {book.chapters} Chapters
              </span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              {book.name}
              <span className="block text-yellow-300">Bible Quiz</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              {book.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href={`/${params.bookSlug}-complete-quiz`}
                className={`${book.testament === 'old' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-purple-600 hover:bg-purple-700'} text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg`}
              >
                Take Complete {book.name} Quiz
              </Link>
              <Link
                href={`/${params.bookSlug}-1-quiz`}
                className="bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Start with Chapter 1
              </Link>
            </div>
          </div>
        </section>

        {/* Book Information */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">About the Book of {book.name}</h2>
                <div className="prose prose-lg max-w-none">
                  <p className="text-gray-600 text-lg leading-relaxed mb-6">
                    {book.description}
                  </p>
                  
                  <div className="grid md:grid-cols-2 gap-8 mt-8">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Key Themes</h3>
                      <ul className="space-y-2">
                        {book.keyThemes.map((theme, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${book.testament === 'old' ? 'bg-blue-500' : 'bg-purple-500'}`} />
                            <span className="text-gray-700">{theme}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Main Characters</h3>
                      <ul className="space-y-2">
                        {book.mainCharacters.map((character, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${book.testament === 'old' ? 'bg-blue-500' : 'bg-purple-500'}`} />
                            <Link 
                              href={`/${character.toLowerCase().replace(/\s+/g, '-')}-quiz`}
                              className={`${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                            >
                              {character}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Book Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Testament:</span>
                    <span className={`font-medium ${book.testament === 'old' ? 'text-blue-600' : 'text-purple-600'}`}>
                      {testament}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Chapters:</span>
                    <span className="font-medium">{book.chapters}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Order:</span>
                    <span className="font-medium">Book #{book.order}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Abbreviation:</span>
                    <span className="font-medium">{book.abbreviation}</span>
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">Quick Stats</h4>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className={`p-3 rounded-lg ${book.testament === 'old' ? 'bg-blue-50' : 'bg-purple-50'}`}>
                      <div className={`text-2xl font-bold ${book.testament === 'old' ? 'text-blue-600' : 'text-purple-600'}`}>
                        {book.chapters}
                      </div>
                      <div className="text-sm text-gray-600">Chapter Quizzes</div>
                    </div>
                    <div className={`p-3 rounded-lg ${book.testament === 'old' ? 'bg-blue-50' : 'bg-purple-50'}`}>
                      <div className={`text-2xl font-bold ${book.testament === 'old' ? 'text-blue-600' : 'text-purple-600'}`}>
                        1
                      </div>
                      <div className="text-sm text-gray-600">Complete Quiz</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Chapter Quizzes Grid */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {book.name} Chapter Quizzes
              </h2>
              <p className="text-xl text-gray-600">
                Choose any chapter to test your knowledge with 16-20 focused questions
              </p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {Array.from({ length: book.chapters }, (_, i) => i + 1).map((chapterNum) => (
                <Link
                  key={chapterNum}
                  href={`/${params.bookSlug}-${chapterNum}-quiz`}
                  className={`group bg-white hover:${book.testament === 'old' ? 'bg-blue-50' : 'bg-purple-50'} border border-gray-200 hover:border-${testamentColor}-300 rounded-lg p-4 text-center transition-all duration-200 shadow-sm hover:shadow-md`}
                >
                  <div className={`text-2xl font-bold text-gray-700 group-hover:text-${testamentColor}-600 mb-2`}>
                    {chapterNum}
                  </div>
                  <div className="text-sm text-gray-500 group-hover:text-gray-700">
                    Chapter {chapterNum}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    ~6 min quiz
                  </div>
                </Link>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href={`/${params.bookSlug}-complete-quiz`}
                className={`inline-flex items-center ${book.testament === 'old' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-purple-600 hover:bg-purple-700'} text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg`}
              >
                Take Complete {book.name} Quiz
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Related Content */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              Continue Your Bible Study Journey
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Character Studies */}
              <div className={`${book.testament === 'old' ? 'bg-blue-50 border-blue-200' : 'bg-purple-50 border-purple-200'} border rounded-xl p-6`}>
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4">
                  <Image
                    src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
                    alt="Character studies"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className={`text-lg font-semibold ${book.testament === 'old' ? 'text-blue-900' : 'text-purple-900'} mb-2`}>
                  Character Studies
                </h3>
                <p className="text-gray-600 mb-4">
                  Explore the lives and lessons of biblical characters from {book.name}.
                </p>
                <div className="space-y-2">
                  {book.mainCharacters.slice(0, 3).map((character, index) => (
                    <Link
                      key={index}
                      href={`/${character.toLowerCase().replace(/\s+/g, '-')}-quiz`}
                      className={`block text-sm ${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                    >
                      {character} Quiz →
                    </Link>
                  ))}
                </div>
              </div>

              {/* Study Guides */}
              <div className={`${book.testament === 'old' ? 'bg-blue-50 border-blue-200' : 'bg-purple-50 border-purple-200'} border rounded-xl p-6`}>
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4">
                  <Image
                    src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
                    alt="Study guides"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className={`text-lg font-semibold ${book.testament === 'old' ? 'text-blue-900' : 'text-purple-900'} mb-2`}>
                  Study Resources
                </h3>
                <p className="text-gray-600 mb-4">
                  Deepen your understanding with comprehensive study materials.
                </p>
                <div className="space-y-2">
                  <Link
                    href={`/${params.bookSlug}-study-guide`}
                    className={`block text-sm ${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                  >
                    {book.name} Study Guide →
                  </Link>
                  <Link
                    href={`/${params.bookSlug}-commentary`}
                    className={`block text-sm ${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                  >
                    Chapter Commentary →
                  </Link>
                  <Link
                    href={`/${params.bookSlug}-reading-plan`}
                    className={`block text-sm ${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                  >
                    Reading Plan →
                  </Link>
                </div>
              </div>

              {/* Related Books */}
              <div className={`${book.testament === 'old' ? 'bg-blue-50 border-blue-200' : 'bg-purple-50 border-purple-200'} border rounded-xl p-6`}>
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4">
                  <Image
                    src="/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png"
                    alt="Related books"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className={`text-lg font-semibold ${book.testament === 'old' ? 'text-blue-900' : 'text-purple-900'} mb-2`}>
                  Related Books
                </h3>
                <p className="text-gray-600 mb-4">
                  Continue with other {testament} books.
                </p>
                <div className="space-y-2">
                  {bibleBooks
                    .filter(b => b.testament === book.testament && b.id !== book.id)
                    .slice(0, 3)
                    .map((relatedBook) => (
                      <Link
                        key={relatedBook.id}
                        href={`/${relatedBook.slug}-quiz`}
                        className={`block text-sm ${book.testament === 'old' ? 'text-blue-600 hover:text-blue-700' : 'text-purple-600 hover:text-purple-700'} hover:underline`}
                      >
                        {relatedBook.name} Quiz →
                      </Link>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* JSON-LD Schema for Quiz */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Quiz',
            name: `${book.name} Bible Quiz`,
            description: `Comprehensive Bible quiz for the book of ${book.name} with ${book.chapters} chapter quizzes and complete book assessment.`,
            about: {
              '@type': 'Thing',
              name: `Book of ${book.name}`
            },
            educationalLevel: 'Intermediate',
            assesses: `Bible Knowledge - ${book.name}`,
            typicalAgeRange: '13-99',
            timeRequired: 'PT15M',
            interactivityType: 'active',
            learningResourceType: 'assessment'
          })
        }}
      />
    </>
  );
}