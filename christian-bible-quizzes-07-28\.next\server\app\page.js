/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Navigation.tsx */ \"(ssr)/./components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ZpY3RvcmlhJTVDJTVDY2hyaXN0aWFuLWJpYmxlLXF1aXp6ZXMtMDctMjglNUMlNUNjb21wb25lbnRzJTVDJTVDTmF2aWdhdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1ZpY3RvcmlhJTVDJTVDY2hyaXN0aWFuLWJpYmxlLXF1aXp6ZXMtMDctMjglNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2ltYWdlLWNvbXBvbmVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNWaWN0b3JpYSU1QyU1Q2NocmlzdGlhbi1iaWJsZS1xdWl6emVzLTA3LTI4JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQWdKO0FBQ2hKO0FBQ0Esc05BQTZJO0FBQzdJO0FBQ0EsZ01BQWtJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2hyaXN0aWFuLWJpYmxlLXF1aXp6ZXMtMDctMjgvP2UwMjMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVmljdG9yaWFcXFxcY2hyaXN0aWFuLWJpYmxlLXF1aXp6ZXMtMDctMjhcXFxcY29tcG9uZW50c1xcXFxOYXZpZ2F0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVmljdG9yaWFcXFxcY2hyaXN0aWFuLWJpYmxlLXF1aXp6ZXMtMDctMjhcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcaW1hZ2UtY29tcG9uZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxWaWN0b3JpYVxcXFxjaHJpc3RpYW4tYmlibGUtcXVpenplcy0wNy0yOFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CVictoria%5C%5Cchristian-bible-quizzes-07-28%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigationItems = [\n    {\n        label: \"Home\",\n        href: \"/\",\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        label: \"Bible Quizzes\",\n        href: \"/bible-quizzes\",\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\",\n        children: [\n            {\n                label: \"All Bible Quizzes\",\n                href: \"/bible-quizzes\"\n            },\n            {\n                label: \"Old Testament\",\n                href: \"/old-testament-quizzes\"\n            },\n            {\n                label: \"New Testament\",\n                href: \"/new-testament-quizzes\"\n            },\n            {\n                label: \"By Difficulty\",\n                href: \"/bible-quiz-difficulty\"\n            },\n            {\n                label: \"Kids & Youth\",\n                href: \"/kids-bible-quiz\"\n            },\n            {\n                label: \"Popular Quizzes\",\n                href: \"/popular-bible-quizzes\"\n            }\n        ]\n    },\n    {\n        label: \"Characters\",\n        href: \"/bible-characters\",\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\",\n        children: [\n            {\n                label: \"All Characters\",\n                href: \"/bible-characters\"\n            },\n            {\n                label: \"Old Testament\",\n                href: \"/old-testament-characters\"\n            },\n            {\n                label: \"New Testament\",\n                href: \"/new-testament-characters\"\n            },\n            {\n                label: \"Women in Bible\",\n                href: \"/women-bible-characters\"\n            }\n        ]\n    },\n    {\n        label: \"Study Guides\",\n        href: \"/bible-study-guides\",\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\",\n        children: [\n            {\n                label: \"All Study Guides\",\n                href: \"/bible-study-guides\"\n            },\n            {\n                label: \"Book Studies\",\n                href: \"/bible-book-studies\"\n            },\n            {\n                label: \"Chapter Studies\",\n                href: \"/bible-chapter-studies\"\n            },\n            {\n                label: \"Reading Plans\",\n                href: \"/bible-reading-plans\"\n            }\n        ]\n    },\n    {\n        label: \"About\",\n        href: \"/about\",\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    }\n];\nfunction Navigation() {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const handleDropdownToggle = (label)=>{\n        setActiveDropdown(activeDropdown === label ? null : label);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50 border-b border-blue-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-10 h-10 rounded-full overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\",\n                                            alt: \"Bible Quizzes Logo\",\n                                            fill: true,\n                                            className: \"object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-blue-900 hidden sm:block\",\n                                        children: \"Bible Quizzes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: \"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\",\n                                            onMouseEnter: ()=>item.children && setActiveDropdown(item.label),\n                                            onMouseLeave: ()=>setActiveDropdown(null),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 ml-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 9l-7 7-7-7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.children && activeDropdown === item.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 top-full mt-1 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                            onMouseEnter: ()=>setActiveDropdown(item.label),\n                                            onMouseLeave: ()=>setActiveDropdown(null),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: child.href,\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200\",\n                                                        children: child.label\n                                                    }, child.label, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleMobileMenu,\n                                className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                                \"aria-expanded\": \"false\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Open main menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"block h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 6h16M4 12h16M4 18h16\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"block h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-25 z-40\",\n                        onClick: toggleMobileMenu\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed top-0 right-0 h-full w-[70%] max-w-sm bg-white shadow-xl z-50 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMobileMenu,\n                                        className: \"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-4\",\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: item.href,\n                                                        className: \"flex items-center space-x-3 text-gray-700 hover:text-blue-600 py-2 text-base font-medium transition-colors duration-200 flex-1\",\n                                                        onClick: ()=>!item.children && toggleMobileMenu(),\n                                                        children: [\n                                                            item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-6 h-6 rounded-full overflow-hidden flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    src: item.imageUrl,\n                                                                    alt: `${item.label} icon`,\n                                                                    fill: true,\n                                                                    className: \"object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDropdownToggle(item.label),\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: `w-4 h-4 transform transition-transform duration-200 ${activeDropdown === item.label ? \"rotate-180\" : \"\"}`,\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.children && activeDropdown === item.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 mt-2 space-y-1\",\n                                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: child.href,\n                                                        className: \"block py-2 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200\",\n                                                        onClick: toggleMobileMenu,\n                                                        children: child.label\n                                                    }, child.label, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, item.label, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\components\\\\Navigation.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"384e4943f0bd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jaHJpc3RpYW4tYmlibGUtcXVpenplcy0wNy0yOC8uL2FwcC9nbG9iYWxzLmNzcz9iNzM1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzg0ZTQ5NDNmMGJkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistVF.woff\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistMonoVF.woff\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Bible Quizzes - Test Your Scripture Knowledge | Christian Bible Quizzes\",\n    description: \"Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. Test your knowledge of Scripture with interactive quizzes, character studies, and chapter-by-chapter assessments.\",\n    keywords: [\n        \"bible quiz\",\n        \"scripture test\",\n        \"bible knowledge\",\n        \"christian quizzes\",\n        \"biblical characters\",\n        \"old testament\",\n        \"new testament\"\n    ],\n    authors: [\n        {\n            name: \"Christian Bible Quizzes\"\n        }\n    ],\n    openGraph: {\n        title: \"Bible Quizzes - Test Your Scripture Knowledge\",\n        description: \"Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. Perfect for Bible study groups, Sunday school, and personal growth.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Bible Quizzes - Test Your Scripture Knowledge\",\n        description: \"Comprehensive Bible quizzes covering all 66 books with 1,000+ questions.\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://yourdomain.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1e40af\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_local_target_css_path_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-gray-50 text-gray-900`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen flex flex-col\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                name: \"Christian Bible Quizzes\",\n                                url: \"https://yourdomain.com\",\n                                logo: \"https://yourdomain.com/logo.png\",\n                                description: \"Comprehensive Bible quizzes for all 66 books with 1,000+ questions covering Scripture knowledge, biblical characters, and spiritual growth.\",\n                                sameAs: [\n                                    \"https://facebook.com/yourpage\",\n                                    \"https://twitter.com/yourhandle\"\n                                ]\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebSite\",\n                                name: \"Christian Bible Quizzes\",\n                                url: \"https://yourdomain.com\",\n                                description: \"Interactive Bible quizzes covering all 66 books of the Bible with detailed explanations and Scripture references.\",\n                                potentialAction: {\n                                    \"@type\": \"SearchAction\",\n                                    target: \"https://yourdomain.com/search?q={search_term_string}\",\n                                    \"query-input\": \"required name=search_term_string\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./components/Navigation.tsx\");\n/* harmony import */ var _data_bible_books__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/bible-books */ \"(rsc)/./data/bible-books.ts\");\n\n\n\n\n\nfunction Home() {\n    const oldTestamentBooks = (0,_data_bible_books__WEBPACK_IMPORTED_MODULE_4__.getBooksByTestament)(\"old\");\n    const newTestamentBooks = (0,_data_bible_books__WEBPACK_IMPORTED_MODULE_4__.getBooksByTestament)(\"new\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative min-h-[600px] flex items-center justify-center overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 z-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\",\n                                        alt: \"Bible verse background\",\n                                        fill: true,\n                                        className: \"object-cover opacity-20\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-900/80 to-purple-900/60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-bold mb-6 leading-tight\",\n                                        children: [\n                                            \"Test Your\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-yellow-300\",\n                                                children: \"Bible Knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto\",\n                                        children: \"Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. Perfect for Bible study groups, Sunday school, and personal spiritual growth.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/bible-quizzes\",\n                                                className: \"bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg\",\n                                                children: \"Start Quiz Journey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/genesis-quiz\",\n                                                className: \"bg-transparent border-2 border-white hover:bg-white hover:text-blue-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300\",\n                                                children: \"Try Genesis Quiz\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-blue-600\",\n                                                children: \"1,189\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600 font-medium\",\n                                                children: \"Chapter Quizzes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-purple-600\",\n                                                children: \"66\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600 font-medium\",\n                                                children: \"Bible Books\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-green-600\",\n                                                children: \"200+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600 font-medium\",\n                                                children: \"Characters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl md:text-4xl font-bold text-red-600\",\n                                                children: \"1,000+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600 font-medium\",\n                                                children: \"Questions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"Featured Bible Quizzes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                            children: \"Start your journey with these popular Bible quizzes covering both Old and New Testament\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\",\n                                                            alt: \"Genesis Quiz\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold\",\n                                                                    children: \"Genesis Quiz\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 103,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90\",\n                                                                    children: \"50 Chapters • 25 Questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: \"Test your knowledge of the book of beginnings - creation, Abraham, Isaac, Jacob, and Joseph.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/genesis-quiz\",\n                                                            className: \"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                                            children: [\n                                                                \"Take Quiz\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 ml-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 5l7 7-7 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 117,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\",\n                                                            alt: \"Matthew Quiz\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold\",\n                                                                    children: \"Matthew Quiz\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90\",\n                                                                    children: \"28 Chapters • 25 Questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: \"Explore the Gospel of Matthew and Jesus as the promised Messiah and King.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/matthew-quiz\",\n                                                            className: \"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                                            children: [\n                                                                \"Take Quiz\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 ml-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 5l7 7-7 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\",\n                                                            alt: \"Psalms Quiz\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 text-white\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold\",\n                                                                    children: \"Psalms Quiz\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm opacity-90\",\n                                                                    children: \"150 Chapters • 25 Questions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-4\",\n                                                            children: \"Dive into the book of worship, prayers, and songs of praise to God.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: \"/psalms-quiz\",\n                                                            className: \"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200\",\n                                                            children: [\n                                                                \"Take Quiz\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 ml-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 5l7 7-7 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                                            children: \"All Bible Books\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600\",\n                                            children: \"Choose from any of the 66 books of the Bible for comprehensive quizzes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-2 gap-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12 rounded-full overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\",\n                                                                alt: \"Old Testament\",\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: \"Old Testament\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                                            children: \"39 Books\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-3 gap-3\",\n                                                    children: oldTestamentBooks.slice(0, 12).map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: `/${book.slug}-quiz`,\n                                                            className: \"bg-gray-50 hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg p-3 text-center transition-all duration-200 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-700 group-hover:text-blue-600\",\n                                                                    children: book.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        book.chapters,\n                                                                        \" chapters\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, book.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/old-testament-quizzes\",\n                                                        className: \"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium\",\n                                                        children: [\n                                                            \"View all Old Testament books\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 ml-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 5l7 7-7 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-12 h-12 rounded-full overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\",\n                                                                alt: \"New Testament\",\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: \"New Testament\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium\",\n                                                            children: \"27 Books\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 sm:grid-cols-3 gap-3\",\n                                                    children: newTestamentBooks.slice(0, 12).map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: `/${book.slug}-quiz`,\n                                                            className: \"bg-gray-50 hover:bg-purple-50 border border-gray-200 hover:border-purple-300 rounded-lg p-3 text-center transition-all duration-200 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-700 group-hover:text-purple-600\",\n                                                                    children: book.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        book.chapters,\n                                                                        \" chapters\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, book.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/new-testament-quizzes\",\n                                                        className: \"inline-flex items-center text-purple-600 hover:text-purple-700 font-medium\",\n                                                        children: [\n                                                            \"View all New Testament books\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 ml-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 5l7 7-7 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-blue-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center opacity-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\",\n                                                alt: \"Bible study background\",\n                                                width: 200,\n                                                height: 200,\n                                                className: \"object-cover rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"relative text-3xl md:text-4xl font-bold text-white mb-4\",\n                                            children: \"Ready to Begin Your Bible Quiz Journey?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\",\n                                    children: \"Join thousands of believers growing in their faith through interactive Bible study. Start with any book or topic that interests you most.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/bible-quizzes\",\n                                            className: \"bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg\",\n                                            children: \"Browse All Quizzes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/bible-characters\",\n                                            className: \"bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300\",\n                                            children: \"Character Studies\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Bible Quizzes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/genesis-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Genesis Quiz\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/matthew-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Matthew Quiz\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/psalms-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Psalms Quiz\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/bible-quizzes\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"All Quizzes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Study Resources\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/bible-study-guides\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Study Guides\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/bible-characters\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Character Studies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/bible-reading-plans\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Reading Plans\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Popular Topics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/miracles-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Miracles Quiz\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/parables-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Parables Quiz\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/ten-commandments-quiz\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Ten Commandments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Site Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/about\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"About Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/contact\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/privacy\",\n                                                    className: \"block hover:text-white\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2025 Christian Bible Quizzes. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\christian-bible-quizzes-07-28\\\\app\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Navigation.tsx":
/*!***********************************!*\
  !*** ./components/Navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\christian-bible-quizzes-07-28\components\Navigation.tsx#default`));


/***/ }),

/***/ "(rsc)/./data/bible-books.ts":
/*!*****************************!*\
  !*** ./data/bible-books.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bibleBooks: () => (/* binding */ bibleBooks),\n/* harmony export */   getAllBookSlugs: () => (/* binding */ getAllBookSlugs),\n/* harmony export */   getAllChapterSlugs: () => (/* binding */ getAllChapterSlugs),\n/* harmony export */   getBookBySlug: () => (/* binding */ getBookBySlug),\n/* harmony export */   getBooksByTestament: () => (/* binding */ getBooksByTestament),\n/* harmony export */   getChapterSlugs: () => (/* binding */ getChapterSlugs)\n/* harmony export */ });\nconst bibleBooks = [\n    // OLD TESTAMENT BOOKS\n    {\n        id: \"genesis\",\n        name: \"Genesis\",\n        slug: \"genesis\",\n        testament: \"old\",\n        chapters: 50,\n        order: 1,\n        abbreviation: \"Gen\",\n        description: \"The book of beginnings - creation, humanity, and God's covenant with Abraham.\",\n        keyThemes: [\n            \"Creation\",\n            \"Fall\",\n            \"Covenant\",\n            \"Abraham\",\n            \"Isaac\",\n            \"Jacob\",\n            \"Joseph\"\n        ],\n        mainCharacters: [\n            \"Adam\",\n            \"Eve\",\n            \"Noah\",\n            \"Abraham\",\n            \"Isaac\",\n            \"Jacob\",\n            \"Joseph\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"exodus\",\n        name: \"Exodus\",\n        slug: \"exodus\",\n        testament: \"old\",\n        chapters: 40,\n        order: 2,\n        abbreviation: \"Exod\",\n        description: \"God delivers Israel from Egypt and establishes the covenant at Mount Sinai.\",\n        keyThemes: [\n            \"Deliverance\",\n            \"Ten Commandments\",\n            \"Tabernacle\",\n            \"Covenant\",\n            \"Law\"\n        ],\n        mainCharacters: [\n            \"Moses\",\n            \"Aaron\",\n            \"Pharaoh\",\n            \"Miriam\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"leviticus\",\n        name: \"Leviticus\",\n        slug: \"leviticus\",\n        testament: \"old\",\n        chapters: 27,\n        order: 3,\n        abbreviation: \"Lev\",\n        description: \"Laws for worship, sacrifice, and holy living for the people of Israel.\",\n        keyThemes: [\n            \"Holiness\",\n            \"Sacrifice\",\n            \"Priesthood\",\n            \"Ceremonial Law\",\n            \"Atonement\"\n        ],\n        mainCharacters: [\n            \"Moses\",\n            \"Aaron\",\n            \"Priests\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"numbers\",\n        name: \"Numbers\",\n        slug: \"numbers\",\n        testament: \"old\",\n        chapters: 36,\n        order: 4,\n        abbreviation: \"Num\",\n        description: \"Israel's wilderness wanderings and preparation to enter the Promised Land.\",\n        keyThemes: [\n            \"Wilderness\",\n            \"Rebellion\",\n            \"Faith\",\n            \"Promise Land\",\n            \"Census\"\n        ],\n        mainCharacters: [\n            \"Moses\",\n            \"Aaron\",\n            \"Joshua\",\n            \"Caleb\",\n            \"Balaam\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"deuteronomy\",\n        name: \"Deuteronomy\",\n        slug: \"deuteronomy\",\n        testament: \"old\",\n        chapters: 34,\n        order: 5,\n        abbreviation: \"Deut\",\n        description: \"Moses' final speeches to Israel before entering the Promised Land.\",\n        keyThemes: [\n            \"Covenant Renewal\",\n            \"Obedience\",\n            \"Love for God\",\n            \"Promised Land\"\n        ],\n        mainCharacters: [\n            \"Moses\",\n            \"Joshua\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"joshua\",\n        name: \"Joshua\",\n        slug: \"joshua\",\n        testament: \"old\",\n        chapters: 24,\n        order: 6,\n        abbreviation: \"Josh\",\n        description: \"The conquest and settlement of the Promised Land under Joshua's leadership.\",\n        keyThemes: [\n            \"Conquest\",\n            \"Faith\",\n            \"Obedience\",\n            \"Promised Land\",\n            \"Covenant\"\n        ],\n        mainCharacters: [\n            \"Joshua\",\n            \"Caleb\",\n            \"Rahab\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"judges\",\n        name: \"Judges\",\n        slug: \"judges\",\n        testament: \"old\",\n        chapters: 21,\n        order: 7,\n        abbreviation: \"Judg\",\n        description: \"The cycle of sin, oppression, and deliverance during the judges period.\",\n        keyThemes: [\n            \"Sin\",\n            \"Deliverance\",\n            \"Leadership\",\n            \"Faithfulness\",\n            \"Apostasy\"\n        ],\n        mainCharacters: [\n            \"Deborah\",\n            \"Gideon\",\n            \"Samson\",\n            \"Samuel\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"ruth\",\n        name: \"Ruth\",\n        slug: \"ruth\",\n        testament: \"old\",\n        chapters: 4,\n        order: 8,\n        abbreviation: \"Ruth\",\n        description: \"A beautiful story of loyalty, love, and God's providence through Ruth and Naomi.\",\n        keyThemes: [\n            \"Loyalty\",\n            \"Love\",\n            \"Providence\",\n            \"Redemption\",\n            \"Family\"\n        ],\n        mainCharacters: [\n            \"Ruth\",\n            \"Naomi\",\n            \"Boaz\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"1-samuel\",\n        name: \"1 Samuel\",\n        slug: \"1-samuel\",\n        testament: \"old\",\n        chapters: 31,\n        order: 9,\n        abbreviation: \"1 Sam\",\n        description: \"The transition from judges to kings, featuring Samuel, Saul, and David.\",\n        keyThemes: [\n            \"Leadership\",\n            \"Kingship\",\n            \"Obedience\",\n            \"God's Choice\",\n            \"Faith\"\n        ],\n        mainCharacters: [\n            \"Samuel\",\n            \"Saul\",\n            \"David\",\n            \"Jonathan\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"2-samuel\",\n        name: \"2 Samuel\",\n        slug: \"2-samuel\",\n        testament: \"old\",\n        chapters: 24,\n        order: 10,\n        abbreviation: \"2 Sam\",\n        description: \"David's reign as king of Israel, his triumphs and failures.\",\n        keyThemes: [\n            \"Kingdom\",\n            \"Covenant\",\n            \"Sin\",\n            \"Forgiveness\",\n            \"Leadership\"\n        ],\n        mainCharacters: [\n            \"David\",\n            \"Bathsheba\",\n            \"Nathan\",\n            \"Absalom\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"1-kings\",\n        name: \"1 Kings\",\n        slug: \"1-kings\",\n        testament: \"old\",\n        chapters: 22,\n        order: 11,\n        abbreviation: \"1 Kgs\",\n        description: \"Solomon's reign and the division of the kingdom into Israel and Judah.\",\n        keyThemes: [\n            \"Wisdom\",\n            \"Temple\",\n            \"Kingdom Division\",\n            \"Idolatry\",\n            \"Prophecy\"\n        ],\n        mainCharacters: [\n            \"Solomon\",\n            \"Elijah\",\n            \"Ahab\",\n            \"Jezebel\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"2-kings\",\n        name: \"2 Kings\",\n        slug: \"2-kings\",\n        testament: \"old\",\n        chapters: 25,\n        order: 12,\n        abbreviation: \"2 Kgs\",\n        description: \"The fall of Israel and Judah, featuring prophets like Elijah and Elisha.\",\n        keyThemes: [\n            \"Fall of Kingdoms\",\n            \"Prophecy\",\n            \"Exile\",\n            \"God's Judgment\",\n            \"Miracles\"\n        ],\n        mainCharacters: [\n            \"Elijah\",\n            \"Elisha\",\n            \"Hezekiah\",\n            \"Josiah\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"1-chronicles\",\n        name: \"1 Chronicles\",\n        slug: \"1-chronicles\",\n        testament: \"old\",\n        chapters: 29,\n        order: 13,\n        abbreviation: \"1 Chr\",\n        description: \"A historical review focusing on David's reign and temple preparations.\",\n        keyThemes: [\n            \"History\",\n            \"Temple\",\n            \"Worship\",\n            \"Genealogy\",\n            \"God's Faithfulness\"\n        ],\n        mainCharacters: [\n            \"David\",\n            \"Solomon\",\n            \"Levites\",\n            \"Temple Musicians\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"2-chronicles\",\n        name: \"2 Chronicles\",\n        slug: \"2-chronicles\",\n        testament: \"old\",\n        chapters: 36,\n        order: 14,\n        abbreviation: \"2 Chr\",\n        description: \"The history of Judah from Solomon to the Babylonian exile.\",\n        keyThemes: [\n            \"Temple Worship\",\n            \"Revival\",\n            \"Exile\",\n            \"God's Mercy\",\n            \"Restoration\"\n        ],\n        mainCharacters: [\n            \"Solomon\",\n            \"Hezekiah\",\n            \"Josiah\",\n            \"Ezra\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"ezra\",\n        name: \"Ezra\",\n        slug: \"ezra\",\n        testament: \"old\",\n        chapters: 10,\n        order: 15,\n        abbreviation: \"Ezra\",\n        description: \"The return from exile and rebuilding of the temple in Jerusalem.\",\n        keyThemes: [\n            \"Restoration\",\n            \"Temple Rebuilding\",\n            \"God's Faithfulness\",\n            \"Reform\"\n        ],\n        mainCharacters: [\n            \"Ezra\",\n            \"Zerubbabel\",\n            \"Cyrus\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"nehemiah\",\n        name: \"Nehemiah\",\n        slug: \"nehemiah\",\n        testament: \"old\",\n        chapters: 13,\n        order: 16,\n        abbreviation: \"Neh\",\n        description: \"Rebuilding Jerusalem's walls and spiritual renewal of the people.\",\n        keyThemes: [\n            \"Rebuilding\",\n            \"Leadership\",\n            \"Prayer\",\n            \"Perseverance\",\n            \"Reform\"\n        ],\n        mainCharacters: [\n            \"Nehemiah\",\n            \"Ezra\",\n            \"Sanballat\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"esther\",\n        name: \"Esther\",\n        slug: \"esther\",\n        testament: \"old\",\n        chapters: 10,\n        order: 17,\n        abbreviation: \"Esth\",\n        description: \"God's providence in protecting His people through Queen Esther.\",\n        keyThemes: [\n            \"Providence\",\n            \"Courage\",\n            \"Deliverance\",\n            \"God's Sovereignty\"\n        ],\n        mainCharacters: [\n            \"Esther\",\n            \"Mordecai\",\n            \"Haman\",\n            \"King Ahasuerus\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"job\",\n        name: \"Job\",\n        slug: \"job\",\n        testament: \"old\",\n        chapters: 42,\n        order: 18,\n        abbreviation: \"Job\",\n        description: \"The problem of suffering and God's sovereignty explored through Job's trials.\",\n        keyThemes: [\n            \"Suffering\",\n            \"Faith\",\n            \"God's Sovereignty\",\n            \"Perseverance\",\n            \"Wisdom\"\n        ],\n        mainCharacters: [\n            \"Job\",\n            \"Eliphaz\",\n            \"Bildad\",\n            \"Zophar\",\n            \"Elihu\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"psalms\",\n        name: \"Psalms\",\n        slug: \"psalms\",\n        testament: \"old\",\n        chapters: 150,\n        order: 19,\n        abbreviation: \"Ps\",\n        description: \"A collection of songs, prayers, and poems expressing worship and lament.\",\n        keyThemes: [\n            \"Worship\",\n            \"Prayer\",\n            \"Trust\",\n            \"Praise\",\n            \"Lament\",\n            \"God's Faithfulness\"\n        ],\n        mainCharacters: [\n            \"David\",\n            \"Asaph\",\n            \"Sons of Korah\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"proverbs\",\n        name: \"Proverbs\",\n        slug: \"proverbs\",\n        testament: \"old\",\n        chapters: 31,\n        order: 20,\n        abbreviation: \"Prov\",\n        description: \"Practical wisdom for daily living and godly character development.\",\n        keyThemes: [\n            \"Wisdom\",\n            \"Knowledge\",\n            \"Understanding\",\n            \"Character\",\n            \"Fear of the Lord\"\n        ],\n        mainCharacters: [\n            \"Solomon\",\n            \"Agur\",\n            \"Lemuel\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"ecclesiastes\",\n        name: \"Ecclesiastes\",\n        slug: \"ecclesiastes\",\n        testament: \"old\",\n        chapters: 12,\n        order: 21,\n        abbreviation: \"Eccl\",\n        description: \"The search for meaning and purpose in life apart from God.\",\n        keyThemes: [\n            \"Vanity\",\n            \"Meaning\",\n            \"Purpose\",\n            \"Fear of God\",\n            \"Wisdom\"\n        ],\n        mainCharacters: [\n            \"The Preacher (Solomon)\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"song-of-solomon\",\n        name: \"Song of Solomon\",\n        slug: \"song-of-solomon\",\n        testament: \"old\",\n        chapters: 8,\n        order: 22,\n        abbreviation: \"Song\",\n        description: \"A poetic celebration of love between a bride and groom.\",\n        keyThemes: [\n            \"Love\",\n            \"Romance\",\n            \"Marriage\",\n            \"Beauty\",\n            \"Devotion\"\n        ],\n        mainCharacters: [\n            \"The Beloved\",\n            \"The Lover\",\n            \"Daughters of Jerusalem\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"isaiah\",\n        name: \"Isaiah\",\n        slug: \"isaiah\",\n        testament: \"old\",\n        chapters: 66,\n        order: 23,\n        abbreviation: \"Isa\",\n        description: \"Prophecies of judgment and salvation, including the suffering Servant.\",\n        keyThemes: [\n            \"Salvation\",\n            \"Messiah\",\n            \"Judgment\",\n            \"Holy One of Israel\",\n            \"Comfort\"\n        ],\n        mainCharacters: [\n            \"Isaiah\",\n            \"Hezekiah\",\n            \"Ahaz\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"jeremiah\",\n        name: \"Jeremiah\",\n        slug: \"jeremiah\",\n        testament: \"old\",\n        chapters: 52,\n        order: 24,\n        abbreviation: \"Jer\",\n        description: \"The weeping prophet's warnings of Jerusalem's destruction and restoration.\",\n        keyThemes: [\n            \"Judgment\",\n            \"New Covenant\",\n            \"Repentance\",\n            \"God's Faithfulness\"\n        ],\n        mainCharacters: [\n            \"Jeremiah\",\n            \"Baruch\",\n            \"Zedekiah\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"lamentations\",\n        name: \"Lamentations\",\n        slug: \"lamentations\",\n        testament: \"old\",\n        chapters: 5,\n        order: 25,\n        abbreviation: \"Lam\",\n        description: \"Mourning poems over Jerusalem's destruction by Babylon.\",\n        keyThemes: [\n            \"Mourning\",\n            \"God's Mercy\",\n            \"Hope\",\n            \"Suffering\",\n            \"Restoration\"\n        ],\n        mainCharacters: [\n            \"Jeremiah (traditional author)\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"ezekiel\",\n        name: \"Ezekiel\",\n        slug: \"ezekiel\",\n        testament: \"old\",\n        chapters: 48,\n        order: 26,\n        abbreviation: \"Ezek\",\n        description: \"Visions of God's glory and prophecies of judgment and restoration.\",\n        keyThemes: [\n            \"God's Glory\",\n            \"Restoration\",\n            \"New Heart\",\n            \"Responsibility\"\n        ],\n        mainCharacters: [\n            \"Ezekiel\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"daniel\",\n        name: \"Daniel\",\n        slug: \"daniel\",\n        testament: \"old\",\n        chapters: 12,\n        order: 27,\n        abbreviation: \"Dan\",\n        description: \"Stories of faithfulness in exile and apocalyptic visions.\",\n        keyThemes: [\n            \"Faithfulness\",\n            \"God's Sovereignty\",\n            \"Prophecy\",\n            \"Kingdom of God\"\n        ],\n        mainCharacters: [\n            \"Daniel\",\n            \"Shadrach\",\n            \"Meshach\",\n            \"Abednego\",\n            \"Nebuchadnezzar\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"hosea\",\n        name: \"Hosea\",\n        slug: \"hosea\",\n        testament: \"old\",\n        chapters: 14,\n        order: 28,\n        abbreviation: \"Hos\",\n        description: \"God's unfailing love depicted through Hosea's marriage to unfaithful Gomer.\",\n        keyThemes: [\n            \"God's Love\",\n            \"Unfaithfulness\",\n            \"Restoration\",\n            \"Covenant\"\n        ],\n        mainCharacters: [\n            \"Hosea\",\n            \"Gomer\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"joel\",\n        name: \"Joel\",\n        slug: \"joel\",\n        testament: \"old\",\n        chapters: 3,\n        order: 29,\n        abbreviation: \"Joel\",\n        description: \"The Day of the Lord and the outpouring of God's Spirit.\",\n        keyThemes: [\n            \"Day of the Lord\",\n            \"Repentance\",\n            \"Spirit's Outpouring\",\n            \"Restoration\"\n        ],\n        mainCharacters: [\n            \"Joel\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"amos\",\n        name: \"Amos\",\n        slug: \"amos\",\n        testament: \"old\",\n        chapters: 9,\n        order: 30,\n        abbreviation: \"Amos\",\n        description: \"A shepherd's call for justice and righteousness in Israel.\",\n        keyThemes: [\n            \"Justice\",\n            \"Righteousness\",\n            \"Social Reform\",\n            \"God's Judgment\"\n        ],\n        mainCharacters: [\n            \"Amos\",\n            \"Amaziah\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"obadiah\",\n        name: \"Obadiah\",\n        slug: \"obadiah\",\n        testament: \"old\",\n        chapters: 1,\n        order: 31,\n        abbreviation: \"Obad\",\n        description: \"Judgment against Edom for their treatment of Judah.\",\n        keyThemes: [\n            \"Divine Justice\",\n            \"Pride\",\n            \"God's Sovereignty\"\n        ],\n        mainCharacters: [\n            \"Obadiah\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"jonah\",\n        name: \"Jonah\",\n        slug: \"jonah\",\n        testament: \"old\",\n        chapters: 4,\n        order: 32,\n        abbreviation: \"Jonah\",\n        description: \"A reluctant prophet's mission to Nineveh and God's mercy.\",\n        keyThemes: [\n            \"Obedience\",\n            \"God's Mercy\",\n            \"Repentance\",\n            \"Mission\"\n        ],\n        mainCharacters: [\n            \"Jonah\",\n            \"Ninevites\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"micah\",\n        name: \"Micah\",\n        slug: \"micah\",\n        testament: \"old\",\n        chapters: 7,\n        order: 33,\n        abbreviation: \"Mic\",\n        description: \"Prophecies of judgment and the promise of a ruler from Bethlehem.\",\n        keyThemes: [\n            \"Justice\",\n            \"Mercy\",\n            \"Messiah\",\n            \"Social Reform\"\n        ],\n        mainCharacters: [\n            \"Micah\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"nahum\",\n        name: \"Nahum\",\n        slug: \"nahum\",\n        testament: \"old\",\n        chapters: 3,\n        order: 34,\n        abbreviation: \"Nah\",\n        description: \"Prophecy of Nineveh's destruction and God's justice.\",\n        keyThemes: [\n            \"Divine Justice\",\n            \"God's Wrath\",\n            \"Comfort for the Oppressed\"\n        ],\n        mainCharacters: [\n            \"Nahum\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"habakkuk\",\n        name: \"Habakkuk\",\n        slug: \"habakkuk\",\n        testament: \"old\",\n        chapters: 3,\n        order: 35,\n        abbreviation: \"Hab\",\n        description: \"A prophet's dialogue with God about injustice and faith.\",\n        keyThemes: [\n            \"Faith\",\n            \"Justice\",\n            \"Trust in God\",\n            \"Prayer\"\n        ],\n        mainCharacters: [\n            \"Habakkuk\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"zephaniah\",\n        name: \"Zephaniah\",\n        slug: \"zephaniah\",\n        testament: \"old\",\n        chapters: 3,\n        order: 36,\n        abbreviation: \"Zeph\",\n        description: \"The Day of the Lord and the promise of restoration.\",\n        keyThemes: [\n            \"Day of the Lord\",\n            \"Judgment\",\n            \"Restoration\",\n            \"Remnant\"\n        ],\n        mainCharacters: [\n            \"Zephaniah\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"haggai\",\n        name: \"Haggai\",\n        slug: \"haggai\",\n        testament: \"old\",\n        chapters: 2,\n        order: 37,\n        abbreviation: \"Hag\",\n        description: \"Encouragement to rebuild the temple after the exile.\",\n        keyThemes: [\n            \"Temple Rebuilding\",\n            \"Priorities\",\n            \"God's Presence\"\n        ],\n        mainCharacters: [\n            \"Haggai\",\n            \"Zerubbabel\",\n            \"Joshua\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"zechariah\",\n        name: \"Zechariah\",\n        slug: \"zechariah\",\n        testament: \"old\",\n        chapters: 14,\n        order: 38,\n        abbreviation: \"Zech\",\n        description: \"Visions of restoration and the coming Messiah.\",\n        keyThemes: [\n            \"Restoration\",\n            \"Messiah\",\n            \"God's Kingdom\",\n            \"Encouragement\"\n        ],\n        mainCharacters: [\n            \"Zechariah\",\n            \"Joshua\",\n            \"Zerubbabel\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"malachi\",\n        name: \"Malachi\",\n        slug: \"malachi\",\n        testament: \"old\",\n        chapters: 4,\n        order: 39,\n        abbreviation: \"Mal\",\n        description: \"The final Old Testament prophet calls for faithfulness and announces the messenger.\",\n        keyThemes: [\n            \"Faithfulness\",\n            \"Messenger\",\n            \"Tithes\",\n            \"Day of the Lord\"\n        ],\n        mainCharacters: [\n            \"Malachi\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    // NEW TESTAMENT BOOKS\n    {\n        id: \"matthew\",\n        name: \"Matthew\",\n        slug: \"matthew\",\n        testament: \"new\",\n        chapters: 28,\n        order: 40,\n        abbreviation: \"Matt\",\n        description: \"The Gospel presenting Jesus as the promised Messiah and King of the Jews.\",\n        keyThemes: [\n            \"Messiah\",\n            \"Kingdom of Heaven\",\n            \"Fulfillment\",\n            \"Discipleship\",\n            \"Teaching\"\n        ],\n        mainCharacters: [\n            \"Jesus\",\n            \"Mary\",\n            \"Joseph\",\n            \"John the Baptist\",\n            \"Peter\",\n            \"Twelve Disciples\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"mark\",\n        name: \"Mark\",\n        slug: \"mark\",\n        testament: \"new\",\n        chapters: 16,\n        order: 41,\n        abbreviation: \"Mark\",\n        description: \"The Gospel emphasizing Jesus as the suffering Servant and Son of God.\",\n        keyThemes: [\n            \"Servant\",\n            \"Miracles\",\n            \"Discipleship\",\n            \"Suffering\",\n            \"Authority\"\n        ],\n        mainCharacters: [\n            \"Jesus\",\n            \"Peter\",\n            \"James\",\n            \"John\",\n            \"Twelve Disciples\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"luke\",\n        name: \"Luke\",\n        slug: \"luke\",\n        testament: \"new\",\n        chapters: 24,\n        order: 42,\n        abbreviation: \"Luke\",\n        description: \"The Gospel presenting Jesus as the perfect Man and Savior of all people.\",\n        keyThemes: [\n            \"Salvation\",\n            \"Compassion\",\n            \"Prayer\",\n            \"Holy Spirit\",\n            \"Universal Gospel\"\n        ],\n        mainCharacters: [\n            \"Jesus\",\n            \"Mary\",\n            \"Elizabeth\",\n            \"Zacharias\",\n            \"Twelve Disciples\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"john\",\n        name: \"John\",\n        slug: \"john\",\n        testament: \"new\",\n        chapters: 21,\n        order: 43,\n        abbreviation: \"John\",\n        description: \"The Gospel revealing Jesus as the divine Son of God and the Word made flesh.\",\n        keyThemes: [\n            \"Eternal Life\",\n            \"Love\",\n            \"Light\",\n            \"Truth\",\n            \"Divinity of Christ\"\n        ],\n        mainCharacters: [\n            \"Jesus\",\n            \"John the Baptist\",\n            \"Mary\",\n            \"Martha\",\n            \"Lazarus\",\n            \"Peter\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"acts\",\n        name: \"Acts\",\n        slug: \"acts\",\n        testament: \"new\",\n        chapters: 28,\n        order: 44,\n        abbreviation: \"Acts\",\n        description: \"The history of the early church and the spread of the Gospel.\",\n        keyThemes: [\n            \"Holy Spirit\",\n            \"Church Growth\",\n            \"Mission\",\n            \"Persecution\",\n            \"Gospel\"\n        ],\n        mainCharacters: [\n            \"Peter\",\n            \"Paul\",\n            \"Stephen\",\n            \"Philip\",\n            \"Barnabas\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"romans\",\n        name: \"Romans\",\n        slug: \"romans\",\n        testament: \"new\",\n        chapters: 16,\n        order: 45,\n        abbreviation: \"Rom\",\n        description: \"Paul's systematic presentation of the Gospel and Christian doctrine.\",\n        keyThemes: [\n            \"Salvation\",\n            \"Grace\",\n            \"Faith\",\n            \"Righteousness\",\n            \"Sin\",\n            \"Justification\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Roman Christians\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"1-corinthians\",\n        name: \"1 Corinthians\",\n        slug: \"1-corinthians\",\n        testament: \"new\",\n        chapters: 16,\n        order: 46,\n        abbreviation: \"1 Cor\",\n        description: \"Paul addresses problems in the Corinthian church and teaches about Christian living.\",\n        keyThemes: [\n            \"Church Unity\",\n            \"Spiritual Gifts\",\n            \"Love\",\n            \"Resurrection\",\n            \"Christian Conduct\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Apollos\",\n            \"Corinthian Christians\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"2-corinthians\",\n        name: \"2 Corinthians\",\n        slug: \"2-corinthians\",\n        testament: \"new\",\n        chapters: 13,\n        order: 47,\n        abbreviation: \"2 Cor\",\n        description: \"Paul defends his ministry and teaches about giving and suffering.\",\n        keyThemes: [\n            \"Ministry\",\n            \"Suffering\",\n            \"Comfort\",\n            \"Giving\",\n            \"Weakness in Strength\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Titus\",\n            \"Corinthian Christians\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"galatians\",\n        name: \"Galatians\",\n        slug: \"galatians\",\n        testament: \"new\",\n        chapters: 6,\n        order: 48,\n        abbreviation: \"Gal\",\n        description: \"Paul defends justification by faith against legalism.\",\n        keyThemes: [\n            \"Justification by Faith\",\n            \"Freedom in Christ\",\n            \"Law vs Grace\",\n            \"Fruit of the Spirit\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Galatian Christians\",\n            \"Peter\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"ephesians\",\n        name: \"Ephesians\",\n        slug: \"ephesians\",\n        testament: \"new\",\n        chapters: 6,\n        order: 49,\n        abbreviation: \"Eph\",\n        description: \"Paul teaches about the Church as the body of Christ and Christian living.\",\n        keyThemes: [\n            \"Church Unity\",\n            \"Spiritual Blessings\",\n            \"Christian Living\",\n            \"Spiritual Warfare\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Ephesian Christians\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"philippians\",\n        name: \"Philippians\",\n        slug: \"philippians\",\n        testament: \"new\",\n        chapters: 4,\n        order: 50,\n        abbreviation: \"Phil\",\n        description: \"Paul's letter of joy and thanksgiving from prison.\",\n        keyThemes: [\n            \"Joy\",\n            \"Humility\",\n            \"Christ's Example\",\n            \"Partnership in Gospel\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Timothy\",\n            \"Epaphroditus\",\n            \"Philippian Christians\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"colossians\",\n        name: \"Colossians\",\n        slug: \"colossians\",\n        testament: \"new\",\n        chapters: 4,\n        order: 51,\n        abbreviation: \"Col\",\n        description: \"Paul teaches about the supremacy of Christ and warns against false teaching.\",\n        keyThemes: [\n            \"Supremacy of Christ\",\n            \"False Teaching\",\n            \"Christian Living\",\n            \"New Life\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Timothy\",\n            \"Epaphras\",\n            \"Colossian Christians\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"1-thessalonians\",\n        name: \"1 Thessalonians\",\n        slug: \"1-thessalonians\",\n        testament: \"new\",\n        chapters: 5,\n        order: 52,\n        abbreviation: \"1 Thess\",\n        description: \"Paul encourages the Thessalonian church and teaches about Christ's return.\",\n        keyThemes: [\n            \"Second Coming\",\n            \"Holy Living\",\n            \"Church Growth\",\n            \"Encouragement\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Silas\",\n            \"Timothy\",\n            \"Thessalonian Christians\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"2-thessalonians\",\n        name: \"2 Thessalonians\",\n        slug: \"2-thessalonians\",\n        testament: \"new\",\n        chapters: 3,\n        order: 53,\n        abbreviation: \"2 Thess\",\n        description: \"Paul corrects misunderstandings about the Day of the Lord.\",\n        keyThemes: [\n            \"Day of the Lord\",\n            \"Perseverance\",\n            \"Work Ethic\",\n            \"False Teaching\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Silas\",\n            \"Timothy\",\n            \"Thessalonian Christians\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"1-timothy\",\n        name: \"1 Timothy\",\n        slug: \"1-timothy\",\n        testament: \"new\",\n        chapters: 6,\n        order: 54,\n        abbreviation: \"1 Tim\",\n        description: \"Paul's pastoral instructions to Timothy for church leadership.\",\n        keyThemes: [\n            \"Church Leadership\",\n            \"Sound Doctrine\",\n            \"Godliness\",\n            \"Pastoral Care\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Timothy\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"2-timothy\",\n        name: \"2 Timothy\",\n        slug: \"2-timothy\",\n        testament: \"new\",\n        chapters: 4,\n        order: 55,\n        abbreviation: \"2 Tim\",\n        description: \"Paul's final letter with encouragement to remain faithful.\",\n        keyThemes: [\n            \"Faithfulness\",\n            \"Endurance\",\n            \"Scripture\",\n            \"Ministry\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Timothy\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"titus\",\n        name: \"Titus\",\n        slug: \"titus\",\n        testament: \"new\",\n        chapters: 3,\n        order: 56,\n        abbreviation: \"Titus\",\n        description: \"Paul's instructions to Titus for organizing the Cretan churches.\",\n        keyThemes: [\n            \"Church Organization\",\n            \"Good Works\",\n            \"Sound Doctrine\",\n            \"Christian Conduct\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Titus\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"philemon\",\n        name: \"Philemon\",\n        slug: \"philemon\",\n        testament: \"new\",\n        chapters: 1,\n        order: 57,\n        abbreviation: \"Phlm\",\n        description: \"Paul's personal appeal for the runaway slave Onesimus.\",\n        keyThemes: [\n            \"Forgiveness\",\n            \"Christian Love\",\n            \"Slavery\",\n            \"Reconciliation\"\n        ],\n        mainCharacters: [\n            \"Paul\",\n            \"Philemon\",\n            \"Onesimus\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"hebrews\",\n        name: \"Hebrews\",\n        slug: \"hebrews\",\n        testament: \"new\",\n        chapters: 13,\n        order: 58,\n        abbreviation: \"Heb\",\n        description: \"The superiority of Christ over the Old Testament system.\",\n        keyThemes: [\n            \"Superiority of Christ\",\n            \"Faith\",\n            \"Perseverance\",\n            \"New Covenant\"\n        ],\n        mainCharacters: [\n            \"Unknown Author\",\n            \"Hebrew Christians\"\n        ],\n        imageUrl: \"/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png\"\n    },\n    {\n        id: \"james\",\n        name: \"James\",\n        slug: \"james\",\n        testament: \"new\",\n        chapters: 5,\n        order: 59,\n        abbreviation: \"James\",\n        description: \"Practical wisdom for Christian living and faith in action.\",\n        keyThemes: [\n            \"Faith and Works\",\n            \"Wisdom\",\n            \"Trials\",\n            \"Practical Christianity\"\n        ],\n        mainCharacters: [\n            \"James\"\n        ],\n        imageUrl: \"/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png\"\n    },\n    {\n        id: \"1-peter\",\n        name: \"1 Peter\",\n        slug: \"1-peter\",\n        testament: \"new\",\n        chapters: 5,\n        order: 60,\n        abbreviation: \"1 Pet\",\n        description: \"Peter encourages suffering Christians to persevere in hope.\",\n        keyThemes: [\n            \"Suffering\",\n            \"Hope\",\n            \"Holy Living\",\n            \"Persecution\"\n        ],\n        mainCharacters: [\n            \"Peter\",\n            \"Silas\"\n        ],\n        imageUrl: \"/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png\"\n    },\n    {\n        id: \"2-peter\",\n        name: \"2 Peter\",\n        slug: \"2-peter\",\n        testament: \"new\",\n        chapters: 3,\n        order: 61,\n        abbreviation: \"2 Pet\",\n        description: \"Peter warns against false teachers and reminds of Christ's return.\",\n        keyThemes: [\n            \"False Teaching\",\n            \"Growth in Grace\",\n            \"Day of the Lord\",\n            \"Scripture\"\n        ],\n        mainCharacters: [\n            \"Peter\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    },\n    {\n        id: \"1-john\",\n        name: \"1 John\",\n        slug: \"1-john\",\n        testament: \"new\",\n        chapters: 5,\n        order: 62,\n        abbreviation: \"1 John\",\n        description: \"John teaches about fellowship with God and love for one another.\",\n        keyThemes: [\n            \"Love\",\n            \"Fellowship\",\n            \"Assurance\",\n            \"Truth vs Error\"\n        ],\n        mainCharacters: [\n            \"John\"\n        ],\n        imageUrl: \"/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png\"\n    },\n    {\n        id: \"2-john\",\n        name: \"2 John\",\n        slug: \"2-john\",\n        testament: \"new\",\n        chapters: 1,\n        order: 63,\n        abbreviation: \"2 John\",\n        description: \"John warns against false teachers and emphasizes love and truth.\",\n        keyThemes: [\n            \"Truth\",\n            \"Love\",\n            \"False Teaching\",\n            \"Hospitality\"\n        ],\n        mainCharacters: [\n            \"John\",\n            \"The Elect Lady\"\n        ],\n        imageUrl: \"/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png\"\n    },\n    {\n        id: \"3-john\",\n        name: \"3 John\",\n        slug: \"3-john\",\n        testament: \"new\",\n        chapters: 1,\n        order: 64,\n        abbreviation: \"3 John\",\n        description: \"John commends Gaius for his hospitality and condemns Diotrephes.\",\n        keyThemes: [\n            \"Hospitality\",\n            \"Church Leadership\",\n            \"Truth\",\n            \"Good vs Evil\"\n        ],\n        mainCharacters: [\n            \"John\",\n            \"Gaius\",\n            \"Diotrephes\",\n            \"Demetrius\"\n        ],\n        imageUrl: \"/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png\"\n    },\n    {\n        id: \"jude\",\n        name: \"Jude\",\n        slug: \"jude\",\n        testament: \"new\",\n        chapters: 1,\n        order: 65,\n        abbreviation: \"Jude\",\n        description: \"Jude warns against false teachers and calls for contending for the faith.\",\n        keyThemes: [\n            \"False Teaching\",\n            \"Contending for Faith\",\n            \"Judgment\",\n            \"Perseverance\"\n        ],\n        mainCharacters: [\n            \"Jude\"\n        ],\n        imageUrl: \"/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png\"\n    },\n    {\n        id: \"revelation\",\n        name: \"Revelation\",\n        slug: \"revelation\",\n        testament: \"new\",\n        chapters: 22,\n        order: 66,\n        abbreviation: \"Rev\",\n        description: \"The apocalyptic vision of Jesus Christ's ultimate victory and the new creation.\",\n        keyThemes: [\n            \"Second Coming\",\n            \"Judgment\",\n            \"Victory\",\n            \"New Heaven\",\n            \"New Earth\",\n            \"Hope\"\n        ],\n        mainCharacters: [\n            \"John\",\n            \"Jesus Christ\",\n            \"Seven Churches\",\n            \"Angels\"\n        ],\n        imageUrl: \"/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png\"\n    }\n];\n// Helper function to get book by slug\nconst getBookBySlug = (slug)=>{\n    return bibleBooks.find((book)=>book.slug === slug);\n};\n// Helper function to get books by testament\nconst getBooksByTestament = (testament)=>{\n    return bibleBooks.filter((book)=>book.testament === testament);\n};\n// Helper function to generate all book quiz URLs\nconst getAllBookSlugs = ()=>{\n    return bibleBooks.map((book)=>book.slug);\n};\n// Generate chapter quiz slugs for a specific book\nconst getChapterSlugs = (bookSlug)=>{\n    const book = getBookBySlug(bookSlug);\n    if (!book) return [];\n    return Array.from({\n        length: book.chapters\n    }, (_, i)=>`${bookSlug}-${i + 1}`);\n};\n// Generate all chapter quiz slugs for all books (1,189 total)\nconst getAllChapterSlugs = ()=>{\n    const allSlugs = [];\n    bibleBooks.forEach((book)=>{\n        const chapterSlugs = getChapterSlugs(book.slug);\n        allSlugs.push(...chapterSlugs);\n    });\n    return allSlugs;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/bible-books.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2NocmlzdGlhbi1iaWJsZS1xdWl6emVzLTA3LTI4Ly4vYXBwL2Zhdmljb24uaWNvPzVhNTYiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CVictoria%5Cchristian-bible-quizzes-07-28&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();