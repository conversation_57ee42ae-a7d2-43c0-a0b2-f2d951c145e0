import { Metadata } from 'next';
import Image from 'next/image';
import Navigation from '@/components/Navigation';

export const metadata: Metadata = {
  title: 'Contact Us | Christian Bible Quizzes',
  description: 'Get in touch with our team for questions, feedback, or support regarding our Bible quizzes and study resources.',
  keywords: ['contact us', 'support', 'feedback', 'bible quiz help', 'customer service'],
  openGraph: {
    title: 'Contact Christian Bible Quizzes',
    description: 'Have questions or feedback? We\'d love to hear from you and help with your Bible study journey.',
    images: ['/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png'],
  },
};

export default function ContactPage() {
  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[400px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png"
              alt="Contact Us"
              fill
              className="object-cover opacity-30"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-700/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Contact
              <span className="block text-yellow-300">Us</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Have questions, feedback, or need support? We're here to help you on your Bible study journey.
            </p>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Get in Touch</h2>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                      <p className="text-gray-600 mb-2">
                        For general questions, feedback, or technical support
                      </p>
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Content Suggestions</h3>
                      <p className="text-gray-600 mb-2">
                        Have ideas for new quizzes or study topics?
                      </p>
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Report Issues</h3>
                      <p className="text-gray-600 mb-2">
                        Found a problem with a quiz or website issue?
                      </p>
                      <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
                
                <form className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Your Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your email address"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select a topic</option>
                      <option value="general">General Question</option>
                      <option value="technical">Technical Support</option>
                      <option value="content">Content Suggestion</option>
                      <option value="bug">Report a Bug</option>
                      <option value="feedback">Feedback</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Please provide details about your question or feedback..."
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                  >
                    Send Message
                  </button>
                </form>

                <p className="text-sm text-gray-500 mt-4">
                  We typically respond within 24-48 hours during business days.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Quick answers to common questions
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  How do I suggest new quiz topics or questions?
                </h3>
                <p className="text-gray-600">
                  We love hearing from our community! Send your <NAME_EMAIL> 
                  with details about the topic, difficulty level, and any specific questions you'd like to see included.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  I found an error in a quiz. How do I report it?
                </h3>
                <p className="text-gray-600">
                  <NAME_EMAIL> with the quiz name, question number, 
                  and details about the error. We review all reports and make corrections promptly.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Can I use your quizzes for my church or Bible study group?
                </h3>
                <p className="text-gray-600">
                  Absolutely! Our quizzes are designed for personal study, small groups, and church education. 
                  Feel free to use them in your ministry activities. For larger scale usage, please contact us.
                </p>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Do you offer study materials for children?
                </h3>
                <p className="text-gray-600">
                  Yes! We have age-appropriate quizzes and study guides designed specifically for children and teens. 
                  Look for content marked with difficulty levels of "Easy" or "Beginner."
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'ContactPage',
            name: 'Contact Christian Bible Quizzes',
            description: 'Get in touch with our team for questions, feedback, or support regarding our Bible quizzes and study resources.',
            url: 'https://yourdomain.com/contact/'
          })
        }}
      />
    </>
  );
}