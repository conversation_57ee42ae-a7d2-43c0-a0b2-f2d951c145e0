import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Bible Quizzes - Test Your Scripture Knowledge | Christian Bible Quizzes",
  description: "Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. Test your knowledge of Scripture with interactive quizzes, character studies, and chapter-by-chapter assessments.",
  keywords: ["bible quiz", "scripture test", "bible knowledge", "christian quizzes", "biblical characters", "old testament", "new testament"],
  authors: [{ name: "Christian Bible Quizzes" }],
  openGraph: {
    title: "Bible Quizzes - Test Your Scripture Knowledge",
    description: "Comprehensive Bible quizzes covering all 66 books with 1,000+ questions. Perfect for Bible study groups, Sunday school, and personal growth.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Bible Quizzes - Test Your Scripture Knowledge",
    description: "Comprehensive Bible quizzes covering all 66 books with 1,000+ questions.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="canonical" href="https://yourdomain.com" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#1e40af" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 text-gray-900`}
      >
        <div className="min-h-screen flex flex-col">
          {children}
        </div>
        
        {/* JSON-LD Schema for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'Christian Bible Quizzes',
              url: 'https://yourdomain.com',
              logo: 'https://yourdomain.com/logo.png',
              description: 'Comprehensive Bible quizzes for all 66 books with 1,000+ questions covering Scripture knowledge, biblical characters, and spiritual growth.',
              sameAs: [
                'https://facebook.com/yourpage',
                'https://twitter.com/yourhandle'
              ]
            })
          }}
        />
        
        {/* JSON-LD Schema for Website */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebSite',
              name: 'Christian Bible Quizzes',
              url: 'https://yourdomain.com',
              description: 'Interactive Bible quizzes covering all 66 books of the Bible with detailed explanations and Scripture references.',
              potentialAction: {
                '@type': 'SearchAction',
                target: 'https://yourdomain.com/search?q={search_term_string}',
                'query-input': 'required name=search_term_string'
              }
            })
          }}
        />
      </body>
    </html>
  );
}