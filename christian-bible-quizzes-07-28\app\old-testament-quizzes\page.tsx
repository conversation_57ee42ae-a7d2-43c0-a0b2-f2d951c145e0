import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { getBooksByTestament } from '@/data/bible-books';

export const metadata: Metadata = {
  title: 'Old Testament Bible Quizzes | Christian Bible Quizzes',
  description: 'Complete collection of Old Testament Bible quizzes covering all 39 books from Genesis to Malachi. Test your knowledge of biblical history, prophecy, and wisdom literature.',
  keywords: ['old testament quiz', 'bible quiz', 'genesis quiz', 'psalms quiz', 'exodus quiz', 'biblical history'],
  openGraph: {
    title: 'Old Testament Bible Quizzes - Complete Collection',
    description: 'Test your Old Testament knowledge with comprehensive quizzes covering all 39 books, from Genesis through Malachi.',
    images: ['/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png'],
  },
};

export default function OldTestamentQuizzesPage() {
  const oldTestamentBooks = getBooksByTestament('old');

  // Group books by category
  const lawBooks = oldTestamentBooks.filter(book => book.order <= 5);
  const historyBooks = oldTestamentBooks.filter(book => book.order > 5 && book.order <= 17);
  const wisdomBooks = oldTestamentBooks.filter(book => book.order > 17 && book.order <= 22);
  const majorProphets = oldTestamentBooks.filter(book => book.order > 22 && book.order <= 27);
  const minorProphets = oldTestamentBooks.filter(book => book.order > 27);

  const categories = [
    {
      title: 'Books of the Law (Torah)',
      description: 'The foundation of faith - Genesis through Deuteronomy',
      books: lawBooks,
      color: 'blue',
      imageUrl: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png'
    },
    {
      title: 'History Books',
      description: 'Chronicles of Israel\'s journey - Joshua through Esther',
      books: historyBooks,
      color: 'green',
      imageUrl: '/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png'
    },
    {
      title: 'Wisdom Literature',
      description: 'Poetry and wisdom - Job through Song of Solomon',
      books: wisdomBooks,
      color: 'purple',
      imageUrl: '/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png'
    },
    {
      title: 'Major Prophets',
      description: 'Isaiah, Jeremiah, Lamentations, Ezekiel, Daniel',
      books: majorProphets,
      color: 'red',
      imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png'
    },
    {
      title: 'Minor Prophets',
      description: 'The Twelve - Hosea through Malachi',
      books: minorProphets,
      color: 'orange',
      imageUrl: '/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png'
    }
  ];

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[500px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
              alt="Old Testament Bible Quizzes"
              fill
              className="object-cover opacity-30"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/80 to-blue-700/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Old Testament
              <span className="block text-yellow-300">Bible Quizzes</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Explore the foundation of faith with comprehensive quizzes covering all 39 books 
              of the Old Testament, from creation to the prophets.
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">39</div>
                <div className="text-sm text-gray-200">Books</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">929</div>
                <div className="text-sm text-gray-200">Chapters</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">4,000+</div>
                <div className="text-sm text-gray-200">Years of History</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">500+</div>
                <div className="text-sm text-gray-200">Quiz Questions</div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-quiz"
                className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start with Genesis
              </Link>
              <Link
                href="#categories"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Browse by Category
              </Link>
            </div>
          </div>
        </section>

        {/* Categories Section */}
        <section id="categories" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Old Testament Categories
              </h2>
              <p className="text-xl text-gray-600">
                Explore the Old Testament by literary category and historical period
              </p>
            </div>

            <div className="space-y-12">
              {categories.map((category, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="p-8">
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="relative w-16 h-16 rounded-full overflow-hidden">
                        <Image
                          src={category.imageUrl}
                          alt={category.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">{category.title}</h3>
                        <p className="text-gray-600">{category.description}</p>
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium bg-${category.color}-100 text-${category.color}-800 mt-2`}>
                          {category.books.length} Books
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {category.books.map((book) => (
                        <Link
                          key={book.id}
                          href={`/${book.slug}-quiz`}
                          className={`bg-${category.color}-50 hover:bg-${category.color}-100 border border-${category.color}-200 hover:border-${category.color}-300 rounded-lg p-4 text-center transition-all duration-200 group`}
                        >
                          <div className={`text-sm font-medium text-${category.color}-800 group-hover:text-${category.color}-900`}>
                            {book.name}
                          </div>
                          <div className={`text-xs text-${category.color}-600 mt-1`}>
                            {book.chapters} chapters
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Popular Quizzes */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Most Popular Old Testament Quizzes
              </h2>
              <p className="text-xl text-gray-600">
                Start with these beloved books of the Old Testament
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {oldTestamentBooks.slice(0, 6).map((book, index) => (
                <div key={book.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={book.imageUrl}
                      alt={book.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{book.name}</h3>
                      <p className="text-sm opacity-90">{book.chapters} Chapters • {book.testament === 'old' ? 'Old Testament' : 'New Testament'}</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {book.description}
                    </p>
                    <Link
                      href={`/${book.slug}-quiz`}
                      className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Take Quiz
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Test Your Old Testament Knowledge?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              From the creation story in Genesis to the prophetic visions of Malachi, 
              explore the rich history and wisdom of the Old Testament.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-quiz"
                className="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Begin with Genesis
              </Link>
              <Link
                href="/bible-quizzes"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                All Bible Quizzes
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Bible Quizzes</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/genesis-quiz" className="block hover:text-white">Genesis Quiz</Link>
                <Link href="/exodus-quiz" className="block hover:text-white">Exodus Quiz</Link>
                <Link href="/psalms-quiz" className="block hover:text-white">Psalms Quiz</Link>
                <Link href="/old-testament-quizzes" className="block hover:text-white">All Old Testament</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Resources</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/bible-study-guides" className="block hover:text-white">Study Guides</Link>
                <Link href="/bible-characters" className="block hover:text-white">Character Studies</Link>
                <Link href="/bible-reading-plans" className="block hover:text-white">Reading Plans</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Popular Topics</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/creation-quiz" className="block hover:text-white">Creation Quiz</Link>
                <Link href="/ten-commandments-quiz" className="block hover:text-white">Ten Commandments</Link>
                <Link href="/prophecy-quiz" className="block hover:text-white">Prophecy Quiz</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Site Info</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/about" className="block hover:text-white">About Us</Link>
                <Link href="/contact" className="block hover:text-white">Contact</Link>
                <Link href="/privacy" className="block hover:text-white">Privacy Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 Christian Bible Quizzes. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: 'Old Testament Bible Quizzes',
            description: 'Complete collection of Old Testament Bible quizzes covering all 39 books from Genesis to Malachi.',
            url: 'https://yourdomain.com/old-testament-quizzes/',
            mainEntity: {
              '@type': 'ItemList',
              numberOfItems: oldTestamentBooks.length,
              itemListElement: oldTestamentBooks.map((book, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: `${book.name} Quiz`,
                url: `https://yourdomain.com/${book.slug}-quiz/`
              }))
            }
          })
        }}
      />
    </>
  );
}