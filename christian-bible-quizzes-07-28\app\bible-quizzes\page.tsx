import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { bibleBooks, getBooksByTestament } from '@/data/bible-books';
import { bibleCharacters, getCharactersByTestament, getWomenOfTheBible } from '@/data/bible-characters';

export const metadata: Metadata = {
  title: 'Bible Quizzes - Complete Collection | Christian Bible Quizzes',
  description: 'Comprehensive collection of Bible quizzes covering all 66 books, 1,189 chapters, 200+ characters, and major themes. Perfect for Bible study groups, Sunday school, and personal growth.',
  keywords: ['bible quiz', 'scripture test', 'bible knowledge', 'christian education', 'bible study', 'old testament quiz', 'new testament quiz'],
  openGraph: {
    title: 'Bible Quizzes - Complete Collection of Scripture Tests',
    description: 'Test your Bible knowledge with 1,000+ questions covering all 66 books, biblical characters, and themes.',
    images: ['/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png'],
  },
};

export default function BibleQuizzesPage() {
  const oldTestamentBooks = getBooksByTestament('old');
  const newTestamentBooks = getBooksByTestament('new');
  const oldTestamentCharacters = getCharactersByTestament('old');
  const newTestamentCharacters = getCharactersByTestament('new');
  const womenOfTheBible = getWomenOfTheBible();

  const featuredQuizzes = [
    {
      title: 'Genesis Quiz',
      description: 'Test your knowledge of the book of beginnings',
      chapters: 50,
      difficulty: 'Easy',
      href: '/genesis-quiz',
      imageUrl: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png',
      color: 'blue'
    },
    {
      title: 'Matthew Quiz',
      description: 'Explore the Gospel of the Messiah and King',
      chapters: 28,
      difficulty: 'Medium',
      href: '/matthew-quiz',
      imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png',
      color: 'purple'
    },
    {
      title: 'Psalms Quiz',
      description: 'Dive into the book of worship and praise',
      chapters: 150,
      difficulty: 'Medium',
      href: '/psalms-quiz',
      imageUrl: '/images/veneeth_john_Close-up_of_hands_clasped_in_prayer_over_an_old_wo_4102fcf6-a02b-451e-978c-3a8e1f9fa12d.png',
      color: 'green'
    },
    {
      title: 'John Quiz',
      description: 'Discover Jesus as the Word made flesh',
      chapters: 21,
      difficulty: 'Medium',
      href: '/john-quiz',
      imageUrl: '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png',
      color: 'indigo'
    }
  ];

  const quizCategories = [
    {
      title: 'Old Testament Quizzes',
      description: 'Explore the foundation of faith with 39 books of the Old Testament',
      count: oldTestamentBooks.length,
      href: '/old-testament-quizzes',
      imageUrl: '/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png',
      color: 'blue'
    },
    {
      title: 'New Testament Quizzes',
      description: 'Study the life of Christ and early church with 27 books',
      count: newTestamentBooks.length,
      href: '/new-testament-quizzes',
      imageUrl: '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png',
      color: 'purple'
    },
    {
      title: 'Character Studies',
      description: 'Learn from the lives of biblical heroes and leaders',
      count: bibleCharacters.length,
      href: '/bible-characters',
      imageUrl: '/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png',
      color: 'green'
    },
    {
      title: 'Thematic Quizzes',
      description: 'Explore major biblical themes and topics',
      count: 50,
      href: '/bible-themes',
      imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png',
      color: 'red'
    }
  ];

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[600px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png"
              alt="Bible Quizzes Collection"
              fill
              className="object-cover opacity-25"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/85 to-purple-900/70" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Complete Bible
              <span className="block text-yellow-300">Quiz Collection</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Test your Scripture knowledge with 1,000+ questions covering all 66 books, 
              biblical characters, and themes. Perfect for personal study and group learning.
            </p>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">1,189</div>
                <div className="text-sm text-gray-200">Chapter Quizzes</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">66</div>
                <div className="text-sm text-gray-200">Bible Books</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">200+</div>
                <div className="text-sm text-gray-200">Characters</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">1,000+</div>
                <div className="text-sm text-gray-200">Questions</div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-quiz"
                className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start with Genesis
              </Link>
              <Link
                href="#categories"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Browse Categories
              </Link>
            </div>
          </div>
        </section>

        {/* Featured Quizzes */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Bible Quizzes
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Start your Bible quiz journey with these popular and engaging studies
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredQuizzes.map((quiz, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={quiz.imageUrl}
                      alt={quiz.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{quiz.title}</h3>
                      <p className="text-sm opacity-90">{quiz.chapters} Chapters • {quiz.difficulty}</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {quiz.description}
                    </p>
                    <Link
                      href={quiz.href}
                      className={`inline-flex items-center bg-${quiz.color}-600 hover:bg-${quiz.color}-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200`}
                    >
                      Take Quiz
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Quiz Categories */}
        <section id="categories" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Quiz Categories
              </h2>
              <p className="text-xl text-gray-600">
                Choose your area of study and dive deep into God's Word
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {quizCategories.map((category, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="flex">
                    <div className="flex-1 p-8">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="relative w-12 h-12 rounded-full overflow-hidden">
                          <Image
                            src={category.imageUrl}
                            alt={category.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">{category.title}</h3>
                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium bg-${category.color}-100 text-${category.color}-800`}>
                            {category.count} Available
                          </span>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-6">
                        {category.description}
                      </p>
                      <Link
                        href={category.href}
                        className={`inline-flex items-center bg-${category.color}-600 hover:bg-${category.color}-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200`}
                      >
                        Explore Category
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Popular Bible Books */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Popular Bible Books
              </h2>
              <p className="text-xl text-gray-600">
                Start with these well-loved books of Scripture
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Old Testament Books */}
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
                      alt="Old Testament"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Old Testament Favorites</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {oldTestamentBooks.slice(0, 8).map((book) => (
                    <Link
                      key={book.id}
                      href={`/${book.slug}-quiz`}
                      className="bg-blue-50 hover:bg-blue-100 border border-blue-200 hover:border-blue-300 rounded-lg p-4 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-blue-800 group-hover:text-blue-900">
                        {book.name}
                      </div>
                      <div className="text-xs text-blue-600 mt-1">
                        {book.chapters} chapters
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center mt-6">
                  <Link
                    href="/old-testament-quizzes"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View all Old Testament books
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* New Testament Books */}
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png"
                      alt="New Testament"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">New Testament Favorites</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {newTestamentBooks.slice(0, 8).map((book) => (
                    <Link
                      key={book.id}
                      href={`/${book.slug}-quiz`}
                      className="bg-purple-50 hover:bg-purple-100 border border-purple-200 hover:border-purple-300 rounded-lg p-4 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-purple-800 group-hover:text-purple-900">
                        {book.name}
                      </div>
                      <div className="text-xs text-purple-600 mt-1">
                        {book.chapters} chapters
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center mt-6">
                  <Link
                    href="/new-testament-quizzes"
                    className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                  >
                    View all New Testament books
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Character Studies Preview */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Biblical Character Studies
              </h2>
              <p className="text-xl text-gray-600">
                Learn from the faith journeys of biblical heroes and leaders
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Major Patriarchs */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4 mx-auto">
                  <Image
                    src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
                    alt="Patriarchs"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 text-center mb-4">Major Patriarchs</h3>
                <div className="space-y-2">
                  {['Abraham', 'Moses', 'David'].map((name) => (
                    <Link
                      key={name}
                      href={`/${name.toLowerCase()}-quiz`}
                      className="block text-center py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                    >
                      {name} Quiz
                    </Link>
                  ))}
                </div>
              </div>

              {/* New Testament Leaders */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4 mx-auto">
                  <Image
                    src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                    alt="Apostles"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 text-center mb-4">Apostles & Leaders</h3>
                <div className="space-y-2">
                  {['Jesus', 'Paul', 'Peter'].map((name) => (
                    <Link
                      key={name}
                      href={`/${name.toLowerCase()}-quiz`}
                      className="block text-center py-2 text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded transition-colors"
                    >
                      {name} Quiz
                    </Link>
                  ))}
                </div>
              </div>

              {/* Women of the Bible */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="relative w-16 h-16 rounded-full overflow-hidden mb-4 mx-auto">
                  <Image
                    src="/images/thienphan9495_Close-up_of_a_womans_hand_holding_a_wooden_rosary_5747c7bd-b033-4a27-9bef-669a2366e489.png"
                    alt="Women of the Bible"
                    fill
                    className="object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 text-center mb-4">Women of Faith</h3>
                <div className="space-y-2">
                  {womenOfTheBible.slice(0, 3).map((character) => (
                    <Link
                      key={character.id}
                      href={`/${character.slug}-quiz`}
                      className="block text-center py-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors"
                    >
                      {character.name} Quiz
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <div className="text-center mt-8">
              <Link
                href="/bible-characters"
                className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Explore All Characters
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center justify-center opacity-10">
                <Image
                  src="/images/thundermifflin_A_close-up_of_a_persons_torso_and_hands_holding__78ccfb79-5831-4d53-acd9-1442c31345d4.png"
                  alt="Bible study"
                  width={200}
                  height={200}
                  className="object-cover rounded-full"
                />
              </div>
              <h2 className="relative text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to Test Your Bible Knowledge?
              </h2>
            </div>
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Join thousands of believers growing in their faith through interactive Bible study. 
              Whether you're a beginner or Bible scholar, there's a perfect quiz waiting for you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/genesis-quiz"
                className="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start Your Journey
              </Link>
              <Link
                href="/random-quiz"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Random Quiz
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Bible Quizzes</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/genesis-quiz" className="block hover:text-white">Genesis Quiz</Link>
                <Link href="/matthew-quiz" className="block hover:text-white">Matthew Quiz</Link>
                <Link href="/psalms-quiz" className="block hover:text-white">Psalms Quiz</Link>
                <Link href="/bible-quizzes" className="block hover:text-white">All Quizzes</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Resources</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/bible-study-guides" className="block hover:text-white">Study Guides</Link>
                <Link href="/bible-characters" className="block hover:text-white">Character Studies</Link>
                <Link href="/bible-reading-plans" className="block hover:text-white">Reading Plans</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Popular Topics</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/miracles-quiz" className="block hover:text-white">Miracles Quiz</Link>
                <Link href="/parables-quiz" className="block hover:text-white">Parables Quiz</Link>
                <Link href="/ten-commandments-quiz" className="block hover:text-white">Ten Commandments</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Site Info</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/about" className="block hover:text-white">About Us</Link>
                <Link href="/contact" className="block hover:text-white">Contact</Link>
                <Link href="/privacy" className="block hover:text-white">Privacy Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 Christian Bible Quizzes. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* JSON-LD Schema for Hub Page */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: 'Bible Quizzes - Complete Collection',
            description: 'Comprehensive collection of Bible quizzes covering all 66 books, 1,189 chapters, and 200+ biblical characters.',
            url: 'https://yourdomain.com/bible-quizzes/',
            mainEntity: {
              '@type': 'ItemList',
              numberOfItems: 1189,
              itemListElement: bibleBooks.slice(0, 10).map((book, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: `${book.name} Quiz`,
                url: `https://yourdomain.com/${book.slug}-quiz/`
              }))
            }
          })
        }}
      />

      {/* FAQ Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'FAQPage',
            mainEntity: [
              {
                '@type': 'Question',
                name: 'How many Bible quizzes are available?',
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: 'We offer 1,189 chapter quizzes covering all 66 books of the Bible, plus character studies and thematic quizzes, totaling over 1,000 questions.'
                }
              },
              {
                '@type': 'Question',
                name: 'Are these Bible quizzes suitable for all ages?',
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: 'Yes, our quizzes are designed for ages 13 and up, with varying difficulty levels from beginner to advanced. Each quiz includes helpful explanations and Bible references.'
                }
              },
              {
                '@type': 'Question',
                name: 'Can I use these quizzes for Bible study groups?',
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: 'Absolutely! Our quizzes are perfect for Bible study groups, Sunday school classes, youth groups, and personal study. Each quiz includes discussion-worthy explanations.'
                }
              }
            ]
          })
        }}
      />
    </>
  );
}