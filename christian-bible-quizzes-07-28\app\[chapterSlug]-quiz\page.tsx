import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import LinearQuiz from '@/components/LinearQuiz';
import Navigation from '@/components/Navigation';
import { getAllChapterSlugs, getBookBySlug, bibleBooks } from '@/data/bible-books';
import { genesisChapter1Quiz, generateGenesisChapterQuiz } from '@/data/genesis-quiz-data';

interface ChapterQuizPageProps {
  params: {
    chapterSlug: string;
  };
}

export async function generateStaticParams() {
  // Generate all chapter quiz slugs (1,189 total)
  const chapterSlugs = getAllChapterSlugs();
  
  return chapterSlugs.map((chapterSlug) => ({
    chapterSlug,
  }));
}

export async function generateMetadata({ params }: ChapterQuizPageProps): Promise<Metadata> {
  const slugParts = params.chapterSlug.split('-');
  const chapterNumber = slugParts[slugParts.length - 1];
  const bookSlug = slugParts.slice(0, -1).join('-');
  const book = getBookBySlug(bookSlug);
  
  if (!book) {
    return {
      title: 'Chapter Quiz Not Found',
    };
  }

  return {
    title: `${book.name} Chapter ${chapterNumber} Quiz - Test Your Bible Knowledge`,
    description: `Test your knowledge of ${book.name} Chapter ${chapterNumber} with this interactive Bible quiz. 16-20 questions covering key verses, characters, and themes with instant results.`,
    keywords: [`${book.name.toLowerCase()} chapter ${chapterNumber} quiz`, `${book.name.toLowerCase()} quiz`, 'bible quiz', 'scripture test', 'bible knowledge'],
    openGraph: {
      title: `${book.name} Chapter ${chapterNumber} Quiz - Bible Knowledge Test`,
      description: `Interactive Bible quiz for ${book.name} Chapter ${chapterNumber} with detailed explanations and Scripture references.`,
      images: [book.imageUrl || '/images/default-bible.png'],
    },
  };
}

function getQuizData(chapterSlug: string) {
  // Parse chapter slug to get book and chapter info
  const slugParts = chapterSlug.split('-');
  const chapterNumber = parseInt(slugParts[slugParts.length - 1]);
  const bookSlug = slugParts.slice(0, -1).join('-');
  const book = getBookBySlug(bookSlug);
  
  if (!book) {
    return null;
  }

  // Return actual quiz data based on book and chapter
  if (bookSlug === 'genesis' && chapterNumber === 1) {
    return genesisChapter1Quiz;
  }
  
  // For other Genesis chapters, generate sample quiz
  if (bookSlug === 'genesis') {
    return generateGenesisChapterQuiz(chapterNumber);
  }
  
  // For other books, generate sample quiz (would be replaced with actual data)
  return {
    id: chapterSlug,
    title: `${book.name} Chapter ${chapterNumber} Quiz`,
    slug: chapterSlug,
    description: `Test your knowledge of ${book.name} Chapter ${chapterNumber} with 16-20 questions covering the key events and teachings.`,
    category: 'chapter' as const,
    difficulty: 'easy' as const,
    questions: [
      {
        id: `${chapterSlug}-1`,
        question: `What is a key theme in ${book.name} Chapter ${chapterNumber}?`,
        type: 'multiple-choice' as const,
        options: ['Faith', 'Hope', 'Love', 'All of the above'],
        correctAnswer: 'All of the above',
        explanation: 'This is a sample question for development purposes.',
        bibleReference: `${book.name} ${chapterNumber}:1`,
        difficulty: 'easy' as const,
        tags: [book.name.toLowerCase(), 'sample']
      }
    ],
    estimatedTime: 6,
    tags: [book.name.toLowerCase(), `chapter-${chapterNumber}`],
    isBookQuiz: false,
    bookName: book.name,
    chapterNumber,
    imageUrl: book.imageUrl,
    metaTitle: `${book.name} Chapter ${chapterNumber} Quiz - Test Your Bible Knowledge`,
    metaDescription: `Test your knowledge of ${book.name} Chapter ${chapterNumber} with this interactive Bible quiz.`,
    keywords: [`${book.name.toLowerCase()} chapter ${chapterNumber} quiz`],
    publishedAt: '2025-01-01',
    updatedAt: '2025-01-01'
  };
}

export default function ChapterQuizPage({ params }: ChapterQuizPageProps) {
  const quiz = getQuizData(params.chapterSlug);
  
  if (!quiz) {
    notFound();
  }

  const slugParts = params.chapterSlug.split('-');
  const chapterNumber = parseInt(slugParts[slugParts.length - 1]);
  const bookSlug = slugParts.slice(0, -1).join('-');
  const book = getBookBySlug(bookSlug);

  if (!book) {
    notFound();
  }

  // Get previous and next chapter links
  const prevChapter = chapterNumber > 1 ? chapterNumber - 1 : null;
  const nextChapter = chapterNumber < book.chapters ? chapterNumber + 1 : null;

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        <LinearQuiz 
          quiz={quiz} 
          showQuiz={true} // CRITICAL: Chapter quizzes bypass description page and use LINEAR format
        />

        {/* Internal Links Section - MANDATORY for SEO */}
        <section className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8 mx-4 sm:mx-6 lg:mx-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            Continue Your Bible Study Journey
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <a 
              href={`/${bookSlug}-${chapterNumber}-study`}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 p-2 rounded hover:bg-blue-100 transition-colors"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              <span>Study {book.name} Chapter {chapterNumber} first</span>
            </a>
            
            {prevChapter && (
              <a 
                href={`/${bookSlug}-${prevChapter}-quiz`}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 p-2 rounded hover:bg-blue-100 transition-colors"
              >
                <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>{book.name} Chapter {prevChapter} Quiz</span>
              </a>
            )}
            
            {nextChapter && (
              <a 
                href={`/${bookSlug}-${nextChapter}-quiz`}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 p-2 rounded hover:bg-blue-100 transition-colors"
              >
                <span>{book.name} Chapter {nextChapter} Quiz</span>
                <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            )}
            
            <a 
              href={`/${bookSlug}-quiz`}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 p-2 rounded hover:bg-blue-100 transition-colors"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <span>Take the complete {book.name} Quiz</span>
            </a>
            
            <a 
              href="/bible-quizzes"
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 p-2 rounded hover:bg-blue-100 transition-colors"
            >
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <span>Browse all Bible Quizzes</span>
            </a>
          </div>
        </section>
      </main>

      {/* JSON-LD Schema for Chapter Quiz */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Quiz',
            name: quiz.title,
            description: quiz.description,
            about: {
              '@type': 'Thing',
              name: `${book.name} Chapter ${chapterNumber}`
            },
            educationalLevel: 'Intermediate',
            assesses: `Bible Knowledge - ${book.name} Chapter ${chapterNumber}`,
            typicalAgeRange: '13-99',
            timeRequired: `PT${quiz.estimatedTime}M`,
            interactivityType: 'active',
            learningResourceType: 'assessment',
            hasPart: quiz.questions.map(q => ({
              '@type': 'Question',
              name: q.question,
              answerCount: q.options?.length || 2,
              acceptedAnswer: {
                '@type': 'Answer',
                text: q.correctAnswer.toString()
              }
            }))
          })
        }}
      />

      {/* Breadcrumb Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: [
              {
                '@type': 'ListItem',
                position: 1,
                name: 'Home',
                item: 'https://yourdomain.com/'
              },
              {
                '@type': 'ListItem',
                position: 2,
                name: 'Bible Quizzes',
                item: 'https://yourdomain.com/bible-quizzes/'
              },
              {
                '@type': 'ListItem',
                position: 3,
                name: `${book.name} Quizzes`,
                item: `https://yourdomain.com/${bookSlug}-quiz/`
              },
              {
                '@type': 'ListItem',
                position: 4,
                name: `${book.name} Chapter ${chapterNumber} Quiz`,
                item: `https://yourdomain.com/${params.chapterSlug}/`
              }
            ]
          })
        }}
      />
    </>
  );
}