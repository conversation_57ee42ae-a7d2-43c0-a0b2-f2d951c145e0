'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NavigationItem } from '@/types/quiz';

const navigationItems: NavigationItem[] = [
  {
    label: 'Home',
    href: '/',
    imageUrl: '/images/mrmkaj_Gentle_hands_holding_an_open_Bible_light_pouring_down_on_ca8c94ca-5316-47b7-a335-94f60bbfc8a8.png'
  },
  {
    label: 'Bible Quizzes',
    href: '/bible-quizzes',
    imageUrl: '/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png',
    children: [
      { label: 'All Bible Quizzes', href: '/bible-quizzes' },
      { label: 'Old Testament', href: '/old-testament-quizzes' },
      { label: 'New Testament', href: '/new-testament-quizzes' },
      { label: 'By Difficulty', href: '/bible-quiz-difficulty' },
      { label: 'Kids & Youth', href: '/kids-bible-quiz' },
      { label: 'Popular Quizzes', href: '/popular-bible-quizzes' }
    ]
  },
  {
    label: 'Characters',
    href: '/bible-characters',
    imageUrl: '/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png',
    children: [
      { label: 'All Characters', href: '/bible-characters' },
      { label: 'Old Testament', href: '/old-testament-characters' },
      { label: 'New Testament', href: '/new-testament-characters' },
      { label: 'Women in Bible', href: '/women-bible-characters' }
    ]
  },
  {
    label: 'Study Guides',
    href: '/bible-study-guides',
    imageUrl: '/images/daasianaxe_can_you_give_me_a_extrem_close_up_of_two_hands_openi_a36524ce-8e97-4a05-a528-000bbec1e819.png',
    children: [
      { label: 'All Study Guides', href: '/bible-study-guides' },
      { label: 'Book Studies', href: '/bible-book-studies' },
      { label: 'Chapter Studies', href: '/bible-chapter-studies' },
      { label: 'Reading Plans', href: '/bible-reading-plans' }
    ]
  },
  {
    label: 'About',
    href: '/about',
    imageUrl: '/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png'
  }
];

export default function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50 border-b border-blue-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative w-10 h-10 rounded-full overflow-hidden">
                <Image
                  src="/images/rocinanterelampago_central_verse_in_the_Bible_--ar_21_--profile_2a944dbf-6229-46ed-bb1e-0b1ec69c620b.png"
                  alt="Bible Quizzes Logo"
                  fill
                  className="object-cover"
                />
              </div>
              <span className="text-xl font-bold text-blue-900 hidden sm:block">
                Bible Quizzes
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div key={item.label} className="relative group">
                <Link
                  href={item.href}
                  className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  onMouseEnter={() => item.children && setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <span>{item.label}</span>
                  {item.children && (
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  )}
                </Link>

                {/* Desktop Dropdown */}
                {item.children && activeDropdown === item.label && (
                  <div 
                    className="absolute left-0 top-full mt-1 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
                    onMouseEnter={() => setActiveDropdown(item.label)}
                    onMouseLeave={() => setActiveDropdown(null)}
                  >
                    <div className="py-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.label}
                          href={child.href}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMobileMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {!isMobileMenuOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu - CRITICAL: MAX 70% SCREEN WIDTH */}
      {isMobileMenuOpen && (
        <div className="lg:hidden">
          {/* Overlay */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-25 z-40"
            onClick={toggleMobileMenu}
          />
          
          {/* Mobile Menu - CONSTRAINED TO MAX 70% WIDTH */}
          <div className="fixed top-0 right-0 h-full w-[70%] max-w-sm bg-white shadow-xl z-50 overflow-y-auto">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <span className="text-lg font-semibold text-gray-900">Menu</span>
              <button
                onClick={toggleMobileMenu}
                className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="py-4">
              {navigationItems.map((item) => (
                <div key={item.label} className="px-4 py-2">
                  <div className="flex items-center justify-between">
                    <Link
                      href={item.href}
                      className="flex items-center space-x-3 text-gray-700 hover:text-blue-600 py-2 text-base font-medium transition-colors duration-200 flex-1"
                      onClick={() => !item.children && toggleMobileMenu()}
                    >
                      {item.imageUrl && (
                        <div className="relative w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
                          <Image
                            src={item.imageUrl}
                            alt={`${item.label} icon`}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <span>{item.label}</span>
                    </Link>
                    {item.children && (
                      <button
                        onClick={() => handleDropdownToggle(item.label)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <svg 
                          className={`w-4 h-4 transform transition-transform duration-200 ${
                            activeDropdown === item.label ? 'rotate-180' : ''
                          }`} 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                    )}
                  </div>

                  {/* Mobile Dropdown */}
                  {item.children && activeDropdown === item.label && (
                    <div className="ml-6 mt-2 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.label}
                          href={child.href}
                          className="block py-2 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200"
                          onClick={toggleMobileMenu}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}