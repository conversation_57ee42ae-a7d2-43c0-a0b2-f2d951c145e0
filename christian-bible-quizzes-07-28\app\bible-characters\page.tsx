import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Navigation from '@/components/Navigation';
import { bibleCharacters, getCharactersByTestament, getWomenOfTheBible } from '@/data/bible-characters';

export const metadata: Metadata = {
  title: 'Bible Characters Quiz | Christian Bible Quizzes',
  description: 'Test your knowledge of biblical characters with comprehensive quizzes covering 200+ figures from both Old and New Testaments. Perfect for Bible study and character education.',
  keywords: ['bible characters quiz', 'biblical figures', 'abraham quiz', 'moses quiz', 'jesus quiz', 'paul quiz', 'women of the bible'],
  openGraph: {
    title: 'Bible Characters Quiz - Test Your Knowledge',
    description: 'Explore the lives of 200+ biblical characters through interactive quizzes and character studies.',
    images: ['/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png'],
  },
};

export default function BibleCharactersPage() {
  const oldTestamentCharacters = getCharactersByTestament('old');
  const newTestamentCharacters = getCharactersByTestament('new');
  const womenOfTheBible = getWomenOfTheBible();

  // Group characters by significance
  const majorCharacters = bibleCharacters.filter(char => char.significance === 'major').slice(0, 12);
  const featuredWomen = womenOfTheBible.slice(0, 6);

  return (
    <>
      <Navigation />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[500px] flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
              alt="Bible Characters"
              fill
              className="object-cover opacity-30"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 to-green-700/60" />
          </div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Bible Characters
              <span className="block text-yellow-300">Quiz Collection</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
              Explore the lives and lessons of 200+ biblical characters through interactive quizzes 
              and comprehensive character studies. From patriarchs to apostles, discover their faith journeys.
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">200+</div>
                <div className="text-sm text-gray-200">Characters</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">66</div>
                <div className="text-sm text-gray-200">Bible Books</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">4,000+</div>
                <div className="text-sm text-gray-200">Years of History</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <div className="text-3xl font-bold text-yellow-300">500+</div>
                <div className="text-sm text-gray-200">Quiz Questions</div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/abraham-quiz"
                className="bg-yellow-500 hover:bg-yellow-600 text-green-900 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start with Abraham
              </Link>
              <Link
                href="#categories"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-green-900 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                Browse Characters
              </Link>
            </div>
          </div>
        </section>

        {/* Major Characters Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Major Biblical Characters
              </h2>
              <p className="text-xl text-gray-600">
                Start with these influential figures who shaped biblical history
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {majorCharacters.map((character) => (
                <div key={character.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={character.imageUrl}
                      alt={character.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{character.name}</h3>
                      <p className="text-sm opacity-90">{character.testament === 'old' ? 'Old Testament' : 'New Testament'}</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {character.description}
                    </p>
                    <div className="mb-4">
                      <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Key Role</div>
                      <div className="text-sm font-medium text-gray-900">{character.role}</div>
                    </div>
                    <Link
                      href={`/${character.slug}-quiz`}
                      className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Take Quiz
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Character Categories */}
        <section id="categories" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Character Categories
              </h2>
              <p className="text-xl text-gray-600">
                Explore biblical characters by testament and role
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* Old Testament Characters */}
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/delightful_dolphin_55571_A_senior_person_in_silhouette_against__7cbab45f-3b0a-4356-9a4b-8d17e7adb0a5.png"
                      alt="Old Testament Characters"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Old Testament Characters</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {oldTestamentCharacters.slice(0, 8).map((character) => (
                    <Link
                      key={character.id}
                      href={`/${character.slug}-quiz`}
                      className="bg-blue-50 hover:bg-blue-100 border border-blue-200 hover:border-blue-300 rounded-lg p-4 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-blue-800 group-hover:text-blue-900">
                        {character.name}
                      </div>
                      <div className="text-xs text-blue-600 mt-1">
                        {character.role}
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center mt-6">
                  <Link
                    href="/old-testament-characters"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View all Old Testament characters
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>

              {/* New Testament Characters */}
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src="/images/alex.iaquinto_4k_close_up_photo_of_man_praying_while_the_glory__281c620b-2697-4bce-88fc-db85b2e1c270.png"
                      alt="New Testament Characters"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">New Testament Characters</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {newTestamentCharacters.slice(0, 8).map((character) => (
                    <Link
                      key={character.id}
                      href={`/${character.slug}-quiz`}
                      className="bg-purple-50 hover:bg-purple-100 border border-purple-200 hover:border-purple-300 rounded-lg p-4 text-center transition-all duration-200 group"
                    >
                      <div className="text-sm font-medium text-purple-800 group-hover:text-purple-900">
                        {character.name}
                      </div>
                      <div className="text-xs text-purple-600 mt-1">
                        {character.role}
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="text-center mt-6">
                  <Link
                    href="/new-testament-characters"
                    className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium"
                  >
                    View all New Testament characters
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Women of the Bible */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Women of the Bible
              </h2>
              <p className="text-xl text-gray-600">
                Discover the faith and courage of biblical women
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredWomen.map((character) => (
                <div key={character.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 group">
                  <div className="relative h-48">
                    <Image
                      src={character.imageUrl}
                      alt={character.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3 className="text-lg font-bold">{character.name}</h3>
                      <p className="text-sm opacity-90">{character.testament === 'old' ? 'Old Testament' : 'New Testament'}</p>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 mb-4 text-sm">
                      {character.description}
                    </p>
                    <div className="mb-4">
                      <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Known For</div>
                      <div className="text-sm font-medium text-gray-900">{character.role}</div>
                    </div>
                    <Link
                      href={`/${character.slug}-quiz`}
                      className="inline-flex items-center bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Take Quiz
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/women-bible-characters"
                className="inline-flex items-center bg-pink-600 hover:bg-pink-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                All Women of the Bible
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-green-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Test Your Character Knowledge?
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              From the faith of Abraham to the courage of Esther, from the wisdom of Solomon to the dedication of Paul, 
              explore the lives that shaped biblical history.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/abraham-quiz"
                className="bg-white hover:bg-gray-100 text-green-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start with Abraham
              </Link>
              <Link
                href="/bible-quizzes"
                className="bg-transparent border-2 border-white hover:bg-white hover:text-green-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-300"
              >
                All Bible Quizzes
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Bible Characters</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/abraham-quiz" className="block hover:text-white">Abraham Quiz</Link>
                <Link href="/moses-quiz" className="block hover:text-white">Moses Quiz</Link>
                <Link href="/david-quiz" className="block hover:text-white">David Quiz</Link>
                <Link href="/bible-characters" className="block hover:text-white">All Characters</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Study Resources</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/bible-study-guides" className="block hover:text-white">Study Guides</Link>
                <Link href="/bible-characters" className="block hover:text-white">Character Studies</Link>
                <Link href="/bible-reading-plans" className="block hover:text-white">Reading Plans</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Popular Topics</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/women-bible-characters" className="block hover:text-white">Women of the Bible</Link>
                <Link href="/old-testament-characters" className="block hover:text-white">OT Characters</Link>
                <Link href="/new-testament-characters" className="block hover:text-white">NT Characters</Link>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Site Info</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <Link href="/about" className="block hover:text-white">About Us</Link>
                <Link href="/contact" className="block hover:text-white">Contact</Link>
                <Link href="/privacy" className="block hover:text-white">Privacy Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>&copy; 2025 Christian Bible Quizzes. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'CollectionPage',
            name: 'Bible Characters Quiz Collection',
            description: 'Comprehensive collection of Bible character quizzes covering 200+ biblical figures from both testaments.',
            url: 'https://yourdomain.com/bible-characters/',
            mainEntity: {
              '@type': 'ItemList',
              numberOfItems: bibleCharacters.length,
              itemListElement: bibleCharacters.slice(0, 10).map((character, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: `${character.name} Quiz`,
                url: `https://yourdomain.com/${character.slug}-quiz/`
              }))
            }
          })
        }}
      />
    </>
  );
}